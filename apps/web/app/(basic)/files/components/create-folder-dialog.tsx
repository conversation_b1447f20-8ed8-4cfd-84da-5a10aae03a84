"use client";

import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Label } from "@ragtop-web/ui/components/label";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";

interface CreateFolderDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateFolder: (folderName: string) => void;
  currentPath: string;
}

interface FormValues {
  folderName: string;
}

/**
 * 创建文件夹对话框组件
 */
export function CreateFolderDialog({
  open,
  onClose,
  onCreateFolder,
  currentPath,
}: CreateFolderDialogProps) {
  const t = useTranslations("web.forms.placeholders");
  const tDialog = useTranslations("web.files.dialog");
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    defaultValues: {
      folderName: "",
    },
  });

  // 处理表单提交
  const onSubmit = (data: FormValues) => {
    onCreateFolder(data.folderName);
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{tDialog("createFolderTitle")}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="folderName">{tDialog("folderName")}</Label>
              <Input
                id="folderName"
                placeholder={t("folderName")}
                {...register("folderName", {
                  required: tDialog("folderNameRequired"),
                  pattern: {
                    value: /^[^\\/:*?"<>|]+$/,
                    message: tDialog("folderNameInvalidChars"),
                  },
                })}
              />
              {errors.folderName && (
                <p className="text-destructive text-sm">{errors.folderName.message}</p>
              )}
            </div>
            <div className="text-muted-foreground text-sm">
              {tDialog("currentPath", { path: currentPath === "/" ? "root" : currentPath })}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {tDialog("cancel")}
            </Button>
            <Button type="submit">{tDialog("create")}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
