"use client";

import { isAuthenticated, useLogin } from "@/service/auth-service";

import { siteConfigAtom } from "@/store/user-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ragtop-web/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useAtomValue } from "jotai";
import { LogIn } from "lucide-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export default function LoginPage() {
  const t = useTranslations("management");
  const router = useRouter();
  const theme = useTheme();
  const isDark = theme.resolvedTheme === "dark";
  const siteConfig = useAtomValue(siteConfigAtom);
  const isLoggedIn = isAuthenticated();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const signinMutation = useLogin();

  // 表单验证模式
  const formSchema = z.object({
    username: z.string().min(2, t("login.usernameMinLength")),
    password: z.string(),
  });

  type FormValues = z.infer<typeof formSchema>;

  // 检查是否已登录
  useEffect(() => {
    if (isLoggedIn) {
      router.push("/users");
    }
  }, []);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // 处理登录
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);
    setError("");
    signinMutation.mutate(values, {
      onSuccess: () => {
        // 跳转到用户页面
        router.push("/users");
      },
      onError: (data) => {
        setError(t("login.loginFailed"));
        setIsLoading(false);
      },
    });
  };

  if (isLoggedIn) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-current border-t-transparent"></div>
          <p className="text-muted-foreground text-sm">{t("login.verifying")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-4 p-4">
      {siteConfig && (
        <Image
          src={
            isDark
              ? `/frontend-api/image-proxy?key=${siteConfig?.dark_logo}`
              : `/frontend-api/image-proxy?key=${siteConfig?.logo}`
          }
          alt="logo"
          width={140}
          height={40}
          unoptimized
        />
      )}
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-xl font-bold">{t("login.title")}</CardTitle>
          <CardDescription>{t("login.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive mb-4 rounded-md p-3 text-sm">
              {error}
            </div>
          )}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("login.usernameLabel")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("login.usernamePlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("login.passwordLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={t("login.passwordPlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {t("login.loggingIn")}
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    {t("login.login")}
                  </span>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      {siteConfig?.name === "MAXIR AI" && (
        <Image src={"/logo/ucloud-grey.svg"} alt="logo" width={140} height={40} className="mt-4" />
      )}
    </div>
  );
}
