"use client";

import { checkIsOwner } from "@/lib/user-role";
import { FileItem } from "@/service";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { PencilIcon, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";

interface FileActionMenuProps {
  file: FileItem;
  // onLinkKnowledgeBase: (file: FileItem) => void
  onRename: (file: FileItem) => void;
  // onMove: (file: FileItem) => void
  onDelete: (file: FileItem) => void;
}

/**
 * 文件操作菜单组件
 */
export function FileActionMenu({
  file,
  // onLinkKnowledgeBase,
  onRename,
  // onMove,
  onDelete,
}: FileActionMenuProps) {
  const t = useTranslations("web.files.actions");
  const creator = file?.creator;
  const isOwner = checkIsOwner(creator?.id, creator?.admin);
  // 空文件夹不显示操作按钮
  const isEmptyFolder = file.isFolder && file.size === 0;

  if (isEmptyFolder) {
    return null;
  }

  return (
    <div className="flex gap-2">
      {/* 链接知识库：本期先不做 */}
      {/* {!file.isFolder && (
          <Button size={"icon"} variant={"ghost"} onClick={() => onLinkKnowledgeBase(file)}>
            <Link className="h-4 w-4 " />
          </Button>
        )} */}

      {/* <Button onClick={() => onMove(file)}>
          <MoveIcon className="h-4 w-4 mr-2" />
          移动
        </Button> */}
      <IconButton
        variant="ghost"
        size="icon"
        tooltip={t("renameFile")}
        onClick={() => onRename(file)}
      >
        <PencilIcon className="h-4 w-4" />
      </IconButton>
      <IconButton
        variant="ghost"
        size="icon"
        tooltip={!isOwner ? t("noPermissionDelete") : t("deleteFile")}
        onClick={() => onDelete(file)}
        disabled={!isOwner}
      >
        <Trash2 className="text-destructive h-4 w-4" />
      </IconButton>
    </div>
  );
}
