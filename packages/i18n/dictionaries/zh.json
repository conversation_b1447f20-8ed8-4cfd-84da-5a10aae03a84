{"web": {"agent": {"actions": {"batchDeleteSessions": "批量删除Session", "clearSessions": "清空Session", "delete": "删除", "deleteAgent": "删除Agent", "edit": "编辑", "editAgent": "编辑Agent"}, "batchDelete": {"cancelButton": "取消", "cancelText": "取消删除操作", "confirmButton": "确认删除", "confirmText": "确认删除选中的会话", "deleting": "删除中...", "description": "您确定要删除选中的 {count} 个会话吗？删除后将无法恢复。", "error": "错误", "failed": "批量删除失败", "noSelection": "请选择要删除的会话", "success": "批量删除成功", "title": "确认批量删除"}, "clear": {"cancelButton": "取消", "cancelText": "取消清空操作", "clearing": "清空中...", "confirmButton": "确认清空", "confirmText": "确认清空所有会话", "description": "您确定要清空所有会话吗？清空后将无法恢复。", "failed": "清空会话失败", "success": "Session清空成功", "title": "确认清空"}, "common": {"agentList": "Agent列表", "assistant": "助手", "cancel": "取消", "chat": "聊天", "confirm": "确认", "error": "错误", "failed": "失败", "loading": "加载中...", "loadingMessage": "正在加载...", "modelNotConfigured": "Agent未配置模型, 不能进行会话,请选择配置模型", "noData": "暂无数据", "notFound": "未找到", "notFoundMessage": "未找到相关内容", "save": "保存", "success": "成功", "untitled": "未命名", "user": "用户"}, "delete": {"cancelButton": "取消", "cancelText": "取消删除操作", "confirmButton": "确认删除", "confirmDescription": "您确定要删除Agent \"{name}\" 吗？删除后将无法恢复。", "confirmText": "确认删除Agent", "confirmTitle": "确认删除", "deleting": "删除中...", "failed": "删除Agent失败", "personalDescription": "删除个人Agent", "success": "Agent删除成功", "teamDescription": "删除团队Agent"}, "details": {"actions": "操作", "advancedSettings": "高级设置", "agentDescription": "Agent描述", "agentName": "Agent名称", "agentPermission": "Agent权限", "agentSettings": "Agent设置", "assistantPrompt": "助手提示词", "basicInfo": "基本信息", "clearSessions": "清空会话", "createTime": "创建时间", "creator": "创建人", "dataset": "数据集", "deleteAgent": "删除Agent", "description": "描述", "editAgent": "编辑Agent", "emptyReply": "空回复", "exportSessions": "导出会话", "flexibility": "灵活性", "greeting": "问候语", "historyContext": "历史上下文", "keywordWeight": "关键词权重", "knowledgeBase": "知识库", "lastActiveTime": "最后活跃时间", "maxTokens": "最大Token数", "model": "模型", "modelConfig": "模型配置", "modelSettings": "模型设置", "name": "名称", "noDescription": "无描述", "none": "无", "notConfigured": "未配置", "notSet": "未设置", "notSetYet": "尚未设置", "permissions": {"personal": "个人", "team": "团队"}, "promptSettings": "提示词设置", "resourceType": "资源类型", "resourceTypes": {"databaseDataset": "数据库数据集", "fileDataset": "文件数据集", "knowledgeBase": "知识库"}, "retrievalCount": "检索数量", "retrievalSettings": "检索设置", "sessionCount": "会话数量", "sessionManagement": "会话管理", "sessionSettings": "会话设置", "similarityThreshold": "相似度阈值", "summaryPrompt": "总结提示词", "supportedLanguages": "支持的语言", "systemPrompt": "系统提示词", "temperature": "随机性", "topP": "核采样", "updateTime": "更新时间", "userPrompt": "用户提示词", "viewSessions": "查看会话"}, "dialog": {"cancel": "取消", "confirm": "确定", "description": "聊天配置描述", "save": "保存", "title": "聊天配置"}, "form": {"agentPermission": "Agent权限", "assistantPrompt": "助手提示词", "balanced": "平衡", "chatModel": "聊天", "creative": "创意", "database": "数据库", "dataset": "数据集", "datasetChatModel": "数据集聊天", "datasets": "数据集", "description": "Agent描述", "descriptionPlaceholder": "请输入Agent描述", "emptyReply": "空回复", "emptyReplyPlaceholder": "请输入空回复内容", "file": "文件", "flexibility": "灵活性", "greeting": "问候语", "greetingPlaceholder": "请输入问候语", "historyContext": "历史上下文", "keywordWeight": "关键词权重", "knowledgeBase": "知识库", "maxTokens": "最大Token数", "model": "模型", "modelNotConfiguredMessage": "团队没有配置{modelType}模型，请联系管理员进行配置", "modelPlaceholder": "请选择模型", "name": "Agent名称", "namePlaceholder": "请输入Agent名称", "personal": "个人", "precise": "精确", "prompt": "提示词", "resourceType": "资源类型", "similarityThreshold": "相似度阈值", "summaryPrompt": "总结提示词", "summaryPromptPlaceholder": "请输入总结提示词", "supportedLanguages": "支持的语言", "supportedLanguagesDescription": "支持的语言描述", "supportedLanguagesPlaceholder": "请输入支持的语言", "systemPrompt": "系统提示词", "systemPromptPlaceholder": "请输入系统提示词", "team": "团队", "temperature": "随机性", "topN": "Top N", "topP": "核采样", "userPrompt": "用户提示词", "validation": {"datasetRequired": "请选择数据集", "descriptionRequired": "Agent描述不能为空", "knowledgeBaseRequired": "请选择知识库", "languageRequired": "请选择语言", "modelRequired": "请选择模型", "nameRequired": "Agent名称不能为空", "permissionRequired": "请选择Agent权限", "promptRequired": "提示词不能为空", "resourceRequired": "请选择资源", "resourceTypeRequired": "请选择资源类型", "systemPromptRequired": "系统提示词不能为空"}}, "helloMessage": "你好！我是你的助手，有什么可以帮到你的吗？", "meta": {"createTime": "创建时间:", "creator": "创建人:", "updateTime": "修改时间:", "updator": "修改人:"}, "session": {"loadingSessionInfo": "加载会话信息中...", "notFound": "未找到", "notFoundMessage": "未找到指定的会话", "title": "会话"}, "tabs": {"basicSettings": "基础设置", "modelSettings": "模型设置", "promptEngine": "提示引擎"}, "tips": {"createFailed": "创建Agent失败", "deleteFailed": "删除Agent失败", "deleteSuccess": "删除Agent成功", "success": "成功", "updateFailed": "更新Agent失败", "updateSuccess": "更新Agent成功"}}, "datasets": {"addDatabaseDataset": "添加数据库数据集", "addFileDataset": "添加文件数据集", "card": {"build": "构建", "building": "构建中", "buildingVersion": "构建中版本:", "createTime": "创建时间:", "creator": "创建人:", "currentVersion": "当前数据版本:", "database": "数据库:", "delete": "删除", "deleteDescription": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "deleteTitle": "确认删除", "description": "描述:", "edit": "编辑", "noPermissionDelete": "您没有权限删除", "none": "无"}, "comingSoon": "此功能即将上线，敬请期待～", "create": "创建数据集", "databaseDataset": "数据库数据集", "databaseDetail": {"build": "构建", "buildError": "构建数据集时发生错误", "buildFailed": "构建失败", "buildStarted": "数据集已开始构建", "building": "构建中", "buildingVersion": "构建中版本", "createTime": "创建时间", "creator": "创建人", "currentVersion": "当前数据版本", "database": "数据库", "datasetList": "数据集列表", "error": "错误", "getColumnsError": "获取表格列数据失败", "loading": "加载中...", "none": "无", "notFound": "未找到指定的数据集", "schema": "<PERSON><PERSON><PERSON>", "selectTablePrompt": "请从左侧选择一个表格查看列数据", "title": "数据集详情"}, "delete": {"description": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteDialog": {"description": "此操作无法撤销。", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "title": "确认删除"}, "emptyMessage": "暂无数据集，点击上方按钮创建第一个数据集", "file": {"worksheet": {"actions": "操作", "cancel": "取消", "columnDescription": "列描述", "columnDescriptionHelp": "详细描述该列的含义、用途或相关信息", "columnDescriptionPlaceholder": "请输入列描述...", "columnName": "列名", "columnNameHelp": "为该列提供一个更易理解的别名", "columnNamePlaceholder": "请输入列名...", "dataType": "数据类型", "edit": "编辑", "originalName": "原文件列名", "save": "确认", "sheetAlias": "Sheet别名:", "sheetAliasHelp": "为该Sheet提供一个更易理解的别名", "sheetAliasPlaceholder": "请输入Sheet别名...", "sheetCommentHelp": "详细描述该Sheet的含义、用途或相关信息", "sheetDescription": "Sheet描述:", "sheetDescriptionPlaceholder": "请输入Sheet描述...", "sheetInfo": "Sheet信息", "subSheetCount": "子表数量:", "tableName": "名称", "unnamed": "未命名"}}, "fileDataset": "文件数据集", "fileDetail": {"actions": {"actions": "操作", "batchOperationFailed": "批量操作失败，请重试", "batchParseError": "没有可解析的文件", "batchStopError": "没有可停止的文件"}, "addFiles": "添加文件", "backToDatasets": "返回数据集列表", "batchDelete": "批量删除", "batchDeleteConfirm": "确定要批量删除 {count} 个文件吗？此操作无法撤销。", "batchParse": "批量解析", "batchReset": "批量重置", "batchStart": "批量开始", "batchStop": "批量停止", "breadcrumb": "文件数据集", "buildDataset": "构建数据集", "configMetadata": "配置元数据", "configNote": "配置过程中数据缓存在本地，请勿刷新页面", "createTime": "创建时间:", "createTime2": "创建时间", "createdBy": "创建者", "creator": "创建人:", "deleteFile": "删除文件", "deleteFileConfirm": "确定要删除文件 \"{fileName}\" 吗？此操作无法撤销。", "documentCount": "文档数量:", "file": "文件", "fileName": "文件名", "fileSize": "文件大小", "hasUnsavedChanges": "有未保存的更改", "loading": "加载中...", "messages": {"addFailed": "添加失败", "addFailedDesc": "添加文件时发生错误，请重试", "addSuccess": "添加成功", "addSuccessDesc": "已成功添加 {count} 个文件到数据集", "batchDeleteFailed": "错误", "batchDeleteFailedDesc": "批量删除失败，请重试", "batchDeleteSuccess": "成功", "batchDeleteSuccessDesc": "已批量删除 {count} 个文件", "deleteFailed": "错误", "deleteFailedDesc": "删除文件时发生错误，请重试", "deleteSuccess": "成功", "deleteSuccessDesc": "已删除文件", "error": "错误", "noPermissionDelete": "错误", "noPermissionDeleteDesc": "您没有删除这些文件的权限", "operationFailed": "错误", "operationFailedDesc": "操作失败，请重试", "parseStartSuccess": "成功", "parseStartSuccessDesc": "已开始解析文件", "parseStopSuccess": "成功", "parseStopSuccessDesc": "已停止解析文件", "resetParseFailed": "重置失败", "resetParseFailedDesc": "重置解析时发生错误，请重试", "resetParseSuccess": "重置成功", "resetParseSuccessDesc": "文件 \"{fileName}\" 的解析已重置", "success": "成功"}, "metadataConfig": "元数据配置", "noPermissionConfig": "您没有权限配置", "noPermissionDelete": "您没有权限删除", "notFound": "指定的文件未找到", "parsing": "解析中", "progress": "进度", "resetParse": "重置解析", "resetParseConfirm": "重置解析后，现有的的元数据配置也会被重置，确定要重置文件 \"{fileName}\" 的解析吗？此操作无法撤销。", "saveAndParse": "保存并解析", "saveConfig": "保存配置", "saveFailed": "保存失败", "saveFailedDesc": "保存元数据配置时发生错误", "saveSuccess": "保存成功", "saveSuccessDesc": "元数据配置已保存并开始解析", "selectWorksheetPrompt": "请从左侧选择一个工作表查看内容", "selectedFilesCount": "已选择 {count} 项", "startParse": "开始解析", "status": "状态", "stopParse": "停止解析", "subtable": "子表", "title": "文件数据集详情", "tooltips": {"cannotResetParse": "当前状态不能重置解析", "deleteFile": "删除文件", "metadataNotParsed": "未解析成功不能进行元信息设置", "metadataSettings": "元信息设置", "noPermissionDelete": "您没有权限删除", "parseCancelling": "取消中，不能执行解析操作", "parseFile": "解析文件", "parseRunning": "解析中暂不支持停止解析", "resetParse": "重置解析", "retryParse": "重试解析"}, "updateTime": "更新时间", "updatedBy": "更新者", "worksheet": "工作表", "worksheets": "工作表"}, "fileForm": {"create": "创建数据集", "description": "数据集描述", "descriptionPlaceholder": "输入数据集描述", "descriptionRequired": "数据集描述不能为空", "exampleLabel": "示例：", "name": "数据集名称", "namePlaceholder": "输入数据集名称", "nameRequired": "数据集名称不能为空", "save": "保存", "supportInfo": "仅支持标准行列结构的CSV / EXCEL文件, 复杂格式的EXCEL文件会解析失败或解析成功查询不准确", "update": "更新数据集"}, "fileSelection": {"addToDataset": "添加到文件数据集", "cancel": "取消", "createTime": "创建时间", "emptyText": "未找到匹配的文件", "endText": "已显示所有文件", "fileList": "文件列表", "filesCount": "{count} 个文件", "loadingText": "加载中...", "maxFilesExceeded": "文件数据集关联的文件数量最大{max}个", "maxFilesLimit": "最多添加{max}个文件({current}/{max})", "rootDirectory": "根目录", "searchPlaceholder": "搜索文件...", "selectedFilesCount": "已选择 {count} 个文件", "size": "大小", "title": "选择文件", "unlinkCount": "未被链接数"}, "form": {"connectionError": "连接发生错误，请稍后重试。", "connectionFailed": "连接失败", "connectionSuccess": "连接成功！数据库可以正常访问。", "create": "创建数据库数据集", "database": "数据库名称", "databaseNote": "仅支持系统内置的分析型数据库", "databaseRequired": "数据库名称不能为空", "description": "数据集描述", "descriptionRequired": "数据集描述不能为空", "mustTestFirst": "请先测试连接成功后再保存", "name": "数据集名称", "nameRequired": "数据集名称不能为空", "password": "数据库密码", "passwordRequired": "密码不能为空", "testConnection": "测试连接", "testing": "测试中...", "update": "编辑数据集", "username": "数据库用户名", "usernameRequired": "用户名不能为空"}, "loadError": "加载数据集列表失败: {error}", "loading": "加载中...", "messages": {"buildError": "构建数据集时发生错误", "buildFailed": "构建失败", "buildStarted": "数据集已开始构建", "buildSuccess": "构建中", "createError": "创建数据集时发生错误", "createFailed": "创建失败", "createSuccess": "创建成功", "databaseDatasetDeleted": "数据库数据集已成功删除", "datasetCreated": "数据集已创建", "datasetUpdated": "数据集已更新", "deleteDatabaseError": "删除数据库数据集时发生错误", "deleteFailed": "删除失败", "deleteFileError": "删除文件数据集时发生错误", "deleteSuccess": "删除成功", "fileDatasetDeleted": "文件数据集已成功删除", "updateError": "更新数据集时发生错误", "updateFailed": "更新失败", "updateSuccess": "更新成功"}, "retry": "重试", "saveChanges": "保存修改", "schemaStructure": "数据库结构", "tabs": {"databaseDataset": "数据库数据集", "fileDataset": "文件数据集"}, "title": "数据集管理", "unknownError": "未知错误"}, "fileManagement": {"actions": "操作", "batchParse": "批量解析", "fileList": "文件列表", "fileName": "文件名", "filesCount": "文件数量", "noFilesFound": "未找到文件", "parseButton": {"parse": "解析", "reparse": "重新解析"}, "parseStatus": "解析状态", "searchPlaceholder": "搜索文件...", "selectedFiles": "已选择的文件", "status": {"failed": "失败", "none": "无", "parsing": "解析中", "success": "成功"}, "title": "文件管理"}, "files": {"actions": {"cancel": "取消", "clearFiles": "清空", "confirm": "确认", "createFolder": "创建文件夹", "deleteFile": "删除文件", "moveFile": "移动文件", "newItem": "新建", "noPermissionDelete": "您没有权限删除", "renameFile": "重命名文件", "retry": "重试", "retryUpload": "重试上传", "selectFiles": "选择文件", "startUpload": "开始上传", "uploadFile": "上传文件"}, "dialog": {"cancel": "取消", "confirm": "确认", "create": "创建", "createFolderTitle": "新建文件夹", "currentName": "当前名称: {name}", "currentPath": "当前位置: {path}", "deleteCancel": "取消", "deleteConfirm": "确认删除", "deleteFileConfirm": "确定要删除这个文件吗？", "deleteFolderConfirm": "确定要删除这个文件夹吗？文件夹内的文件可能被知识库或文件数据集链接，删除后，该文件夹下的所有文件和子文件夹将被删除，链接关系也会被取消，确定要删除吗？", "deleteLinkedFileConfirm": "该文件正在被 {linkNum} 个知识库或文件数据集链接。删除后链接关系也会被取消，确定要删除吗？", "deleteTitle": "删除文件", "folderName": "文件夹名称", "folderNameInvalidChars": "文件夹名称不能包含特殊字符 \\ / : * ? \" < > |", "folderNameRequired": "文件夹名称不能为空", "moveTitle": "移动{type}", "nameInvalidChars": "名称不能包含特殊字符 \\ / : * ? \" < > |", "nameRequired": "名称不能为空", "newName": "新名称", "newNamePlaceholder": "请输入新的{type}名称", "renameFileTitle": "重命名{type}", "renamePlaceholder": "输入新的{type}名称", "renameTitle": "重命名{type}", "types": {"file": "文件", "folder": "文件夹"}}, "emptyState": "暂无知识库，点击上方按钮创建第一个知识库", "loadError": "加载文件列表失败，请刷新页面重试", "loading": "加载中...", "messages": {"createFailed": "创建知识库失败", "createFailedDesc": "创建知识库时发生错误", "createFolderFailed": "创建失败", "createFolderFailedDesc": "创建文件夹时发生错误", "createFolderSuccess": "创建成功", "createFolderSuccessDesc": "文件夹 {folderName} 已创建", "createSuccess": "知识库已创建", "deleteFailed": "删除失败", "deleteFailedDesc": "删除时发生错误", "deleteSuccess": "删除成功", "deleteSuccessDesc": "文件已成功删除", "filesSuccessCount": "{count} 个文件上传成功", "renameFolderFailed": "重命名失败", "renameFolderFailedDesc": "重命名文件时发生错误", "renameFolderSuccess": "重命名成功", "renameFolderSuccessDesc": "文件已重命名为 {newName}", "updateFailed": "更新知识库失败", "updateFailedDesc": "更新知识库时发生错误", "updateSuccess": "知识库已更新", "uploadSuccessCount": "上传 {count} 个文件成功"}, "pagination": {"nextPage": "下一页", "pageInfo": "第 {current} 页，共 {total} 页", "previousPage": "上一页", "totalRecords": "(共 {count} 条记录)"}, "status": {"uploadFailed": "上传失败", "uploadSuccess": "上传成功", "uploading": "上传中", "userCancel": "用户取消", "waitingUpload": "等待上传"}, "table": {"actions": "操作", "createTime": "创建时间", "creator": "创建者", "linkedCount": "链接数量", "name": "名称", "size": "大小", "type": "类型", "updateTime": "更新时间", "updater": "更新者"}, "title": "文件管理", "types": {"file": "文件", "folder": "文件夹"}, "upload": {"clear": "清空", "fileLimits": "最多可选择 {maxCount} 个文件, excel, csv 单个文件最大{tableLimit}MB, 其他文件单个文件最大 {fileLimit}MB", "retry": "重试", "reupload": "重新上传", "selectFiles": "选择文件", "selectedFiles": "已选择的文件 ({count})", "startUpload": "开始上传", "supportedFormats": "支持格式如下： {formats}", "title": "上传文件", "uploadFailed": "上传失败", "uploadProgress": "上传中 {percent}%", "uploadSuccess": "上传成功", "uploadSuccessCount": "上传{count}个文件成功", "uploading": "上传中", "userCancelled": "用户取消", "waitingUpload": "等待上传"}}, "forms": {"placeholders": {"database": "输入数据库", "datasetDescription": "输入数据集描述", "datasetName": "输入数据集名称", "description": "输入描述...", "folderName": "输入文件夹名称", "knowledgeBaseDescription": "输入知识库描述", "knowledgeBaseName": "输入知识库名称", "password": "输入密码", "search": "搜索...", "searchFiles": "搜索文件...", "searchMembers": "搜索成员...", "username": "输入用户名"}, "selectOptions": {"defaultSliceMethod": "默认切片方法", "pdfParser": "PDF解析器", "sliceMethod": "切片方法"}}, "home": {"adminDescription": " 作为团队管理员，您可以管理团队成员和配置模型。", "description": "是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。", "userDescription": " 您可以访问团队共享的资源和创建个人资源。", "welcomeBack": "欢迎回来", "welcomeTitle": "欢迎使用 {siteName}"}, "knowledgeBase": {"addKnowledgeBase": "添加知识库", "addNew": "添加新的", "card": {"creator": "创建者", "delete": "删除", "description": "描述", "edit": "编辑", "fileCount": "文件数量", "filesCount": "文件数量", "noPermissionDelete": "您没有权限删除"}, "chunkMethod": {"cancel": "取消", "chunkSize": "切片大小", "chunkSizeMinError": "切片大小不能小于128", "currentMethod": "当前切片方法: {method}", "description": "为文件 {fileName} 设置切片方法", "save": "保存", "selectNewMethod": "选择新的切片方法", "selectSliceMethod": "选择切片方法", "sliceMethod": "切片方法", "sliceMethodDescription": "选择文档切片的方法", "sliceMethodRequired": "切片方法不能为空", "title": "切片方法设置"}, "create": "创建知识库", "delete": {"description": "如果知识库已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteDialog": {"description": "此操作无法撤销。", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "title": "确认删除"}, "details": {"actions": "操作", "addFiles": "添加文件", "addFilesFailed": "添加文件失败", "addFilesSuccess": "添加文件成功", "backToList": "返回列表", "batchDelete": "批量删除", "batchDeleteFailed": "批量删除失败", "batchDeleteSuccess": "批量删除成功", "batchOperationFailed": "批量操作失败", "batchParse": "批量解析", "batchStart": "批量开始", "batchStop": "批量停止", "batchStopSuccess": "批量停止成功", "cannotParseWhileCancelling": "取消中不能解析", "cannotSetChunkMethod": "不能设置切片方法", "chunkCount": "切片数量", "chunkMethodSetFailed": "设置切片方法失败", "chunkMethodSetSuccess": "设置切片方法成功", "chunkSize": "切片大小", "clearSelection": "清除选择", "createTime": "创建时间", "createTime2": "创建时间", "createdBy": "创建者", "creator": "创建者", "defaultKnowledgeBaseName": "默认知识库名称", "defaultSliceMethod": "默认切片方法", "deleteDialog": {"description": "此操作无法撤销。", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "title": "确认删除"}, "deleteFile": "删除文件", "deleteFileFailed": "删除文件失败", "deleteFileSuccess": "删除文件成功", "documentCount": "文档数量", "documentDisabled": "文档已禁用", "documentEnabled": "文档已启用", "enabled": "已启用", "errorTitle": "错误", "fileName": "文件名", "fileSize": "文件大小", "filesInParseQueue": "解析队列中的文件", "loading": "加载中...", "loadingError": "加载错误", "noFilesToParse": "没有可解析的文件", "noFilesToStop": "没有可停止的文件", "noPermissionBatchDelete": "没有批量删除权限", "noPermissionDelete": "没有删除权限", "noSelection": "未选择", "notFound": "未找到", "notSet": "未设置", "operationFailed": "操作失败", "parseFile": "解析文件", "progress": "进度", "retryParse": "重试解析", "selectAll": "全选", "selectedFilesCount": "已选择文件数量", "setChunkMethod": "设置切片方法", "sliceMethod": "切片方法", "startParseSuccess": "开始解析成功", "status": "状态", "stopParse": "停止解析", "stopParseSuccess": "停止解析成功", "successTitle": "成功", "title": "标题", "updateTime": "更新时间", "updateTime2": "更新时间", "updatedBy": "更新者"}, "edit": "编辑知识库", "emptyMessage": "暂无知识库，点击上方按钮创建第一个知识库", "fileSelection": {"addToKnowledgeBase": "添加到知识库", "breadcrumbHome": "首页", "cancel": "取消", "confirm": "确认", "createTime": "创建时间", "description": "文件选择描述", "emptyText": "暂无文件", "endText": "已显示所有文件", "fileList": "文件列表", "filesCount": "{count} 个文件", "loadingText": "加载中...", "rootDirectory": "根目录", "searchPlaceholder": "搜索文件...", "selectFiles": "选择文件", "selectedCount": "已选择数量", "selectedFilesCount": "已选择 {count} 个文件", "size": "大小", "title": "文件选择", "unlinkCount": "未被链接数"}, "form": {"defaultSliceMethod": "默认切片方法", "description": "知识库描述", "descriptionRequired": "知识库描述不能为空", "name": "知识库名称", "nameRequired": "知识库名称不能为空", "pdfParser": "PDF解析器", "sliceMethod": "切片方法", "sliceMethodDescription": "切片方法描述", "sliceMethodRequired": "切片方法不能为空", "usesDeepDoc": "使用 DeepDOC 解析 PDF 文档"}, "loadError": "加载知识库列表失败: {error}", "loading": "加载中...", "messages": {"addFilesFailed": "添加文件失败", "addFilesSuccess": "添加文件成功", "batchDeleteFailed": "批量删除失败", "batchDeleteSuccess": "批量删除成功", "batchStartFailed": "批量开始失败", "batchStartSuccess": "批量开始成功", "batchStopFailed": "批量停止失败", "batchStopSuccess": "批量停止成功", "createFailed": "创建失败", "createSuccess": "创建成功", "deleteFailed": "删除失败", "deleteFileFailed": "删除文件失败", "deleteFileSuccess": "删除文件成功", "deleteSuccess": "删除成功", "startParseFailed": "开始解析失败", "startParseSuccess": "开始解析成功", "stopParseFailed": "停止解析失败", "stopParseSuccess": "停止解析成功", "updateFailed": "更新失败", "updateSuccess": "更新成功"}, "operations": {"delete": "删除", "refresh": "刷新", "settings": "设置", "start": "开始", "stop": "停止"}, "retry": "重试", "saveChanges": "保存更改", "status": {"cancelled": "已取消", "cancelling": "取消中", "failed": "失败", "none": "无", "parseFailed": "解析失败", "parseSuccess": "解析成功", "parsing": "解析中", "parsingProgress": "解析进度", "queueing": "排队中", "success": "成功", "unknown": "未知", "unparsed": "未解析"}, "title": "知识库管理", "unknownError": "未知错误"}, "login": {"description": "请输入用户名和密码登录", "password": "密码", "passwordPlaceholder": "输入密码", "submitting": "登录中...", "title": "登录", "username": "用户名", "usernameMin": "用户名至少需要2个字符"}, "model": {"cancel": "取消", "chatModel": "聊天模型", "confirm": "确认", "description": "描述", "embeddingModel": "嵌入模型", "nl2pythonModel": "NL2Python模型", "nl2sqlModel": "NL2SQL模型", "placeholder": "请选择一个合适的模型", "rerankModel": "重排序模型", "save": "保存", "saveFailed": "保存模型配置失败", "saveSuccess": "模型配置保存成功", "saving": "保存中...", "selectChatModel": "选择聊天模型", "selectEmbeddingModel": "选择嵌入模型", "submitting": "提交中...", "title": "设置默认模型", "validation": {"selectChatModel": "请选择适当的聊天模型", "selectEmbeddingModel": "请选择适当的嵌入模型", "selectModel": "请选择模型"}}, "password": {"cancel": "取消", "changeDescription": "请输入新密码以完成修改", "changeFailed": "修改密码失败，请重试", "changeSuccess": "密码修改成功！对话框将自动关闭...", "changeTitle": "修改帐户密码", "confirm": "确认修改", "confirmPassword": "确认新密码", "confirmPasswordPlaceholder": "再次输入新密码", "newPassword": "新密码", "newPasswordPlaceholder": "输入新密码", "submitting": "提交中...", "validation": {"confirmPasswordRequired": "确认密码至少需要6个字符", "newPasswordRequired": "新密码至少需要6个字符", "passwordNotMatch": "新密码与确认密码不匹配"}}, "session": {"loadingSessionInfo": "加载会话信息中...", "notFound": "未找到指定的会话", "notFoundMessage": "未找到指定的会话", "title": "会话"}, "sidebar": {"agent": {"addAgent": "添加Agent", "agentList": "Agent列表", "createSuccess": "Agent 创建成功", "myAgent": "我的Agent", "teamAgent": "团队Agent"}, "projects": {"datasets": "数据集", "files": "文件", "knowledgeBase": "知识库", "modelConfig": "模型配置", "teamManagement": "团队管理"}, "team": {"admin": "管理员", "enterpriseSpace": "企业空间", "member": "成员", "personalSpace": "个人空间", "personalSpaceSuffix": "的个人空间", "spaceSuffix": "的空间"}, "user": {"changePassword": "修改密码", "defaultUser": "用户", "logout": "退出登录", "personalSpace": "个人空间", "personalSpaceSuffix": "的个人空间", "spaceSuffix": "的空间"}}, "sliceMethod": {"naive": {"description": "默认切片大小为512，用户可以单独设置每个文件"}, "supportedFormats": "该切片方法支持如下文件格式:"}, "team": {"accountName": "账户名称", "actions": "操作", "active": "活跃", "addMember": "添加成员", "addMembers": {"addButton": "添加按钮", "description": "添加成员描述", "emptyText": "暂无成员", "endText": "已到底部", "loadingText": "加载中...", "searchPlaceholder": "搜索成员...", "selectedCount": "已选择数量", "title": "添加成员", "unknownUser": "未知用户"}, "admin": "管理员", "delete": "删除", "deleteMember": {"description": "您确定要删除成员 \"{memberName}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteMemberTip": "删除成员提示", "email": "邮箱", "inactive": "不活跃", "joinTime": "加入时间", "lastActive": "最后活跃", "makeAdmin": "设为管理员", "member": "成员", "messages": {"addFailed": "添加成员失败", "addSuccess": "成员添加成功", "deleteFailed": "删除失败", "deleteSuccess": "删除成功", "fetchFailed": "获取可添加成员失败", "removeFailed": "移除成员失败", "removeSuccess": "成员移除成功"}, "name": "姓名", "pending": "待确认", "removeAdmin": "取消管理员", "removeMember": "移除成员", "role": "角色", "searchPlaceholder": "搜索成员...", "selectedCount": "已选择 {count} 名成员", "status": "状态", "title": "团队成员", "unknownUser": "未知用户"}, "test": {"button": {"cancel": "", "save": ""}, "description": "", "instructions": "", "note": "", "title": ""}}, "management": {"common": {"actions": "操作", "active": "活跃", "add": "添加", "and": "和", "apply": "应用", "back": "返回", "cancel": "取消", "clear": "清除", "close": "关闭", "confirm": "确认", "confirmPassword": "确认新密码", "create": "创建", "delete": "删除", "description": "描述", "deselect": "取消选择", "deselectAll": "取消全选", "details": "详情", "disabled": "禁用", "download": "下载", "edit": "编辑", "empty": "空", "enabled": "启用", "error": "错误", "export": "导出", "failed": "失败", "false": "否", "filter": "筛选", "forgotPassword": "忘记密码", "hidden": "隐藏", "import": "导入", "inactive": "非活跃", "invalid": "无效", "loading": "加载中...", "login": "登录", "loginWith": "使用 {provider} 登录", "logout": "退出登录", "name": "名称", "newPassword": "新密码", "next": "下一步", "no": "否", "noData": "暂无数据", "noResults": "暂无结果", "off": "关", "offline": "离线", "ok": "确定", "on": "开", "online": "在线", "optional": "可选", "or": "或", "password": "密码", "previous": "上一步", "private": "私有", "profile": "个人资料", "public": "公开", "refresh": "刷新", "register": "注册", "rememberMe": "记住我", "required": "必填", "reset": "重置", "retry": "重试", "save": "保存", "search": "搜索", "select": "选择", "selectAll": "全选", "settings": "设置", "sort": "排序", "status": "状态", "submit": "提交", "submitting": "提交中...", "success": "成功", "time": "时间", "true": "是", "update": "更新", "upload": "上传", "username": "用户名", "valid": "有效", "view": "查看", "visible": "可见", "yes": "是"}, "form": {"emptyAdmin": "请至少选择一个团队管理员", "emptyLoginName": "管理端登录用户名不能为空", "emptyLoginPassword": "管理端登录密码不能为空", "emptyModel": "请选择适当的模型", "emptySiteName": "站点名称不能为空", "emptyTeamName": "团队名称不能为空", "enterTeamName": "请输入团队名称", "inputTeamName": "输入团队名称", "required": "必填", "selectAdminRequired": "请至少选择一个团队管理员"}, "login": {"description": "请输入您的用户名和密码登录系统", "loggingIn": "登录中...", "login": "登录", "loginFailed": "登录失败，请重试", "passwordLabel": "密码", "passwordPlaceholder": "输入密码", "title": "管理后台登录", "usernameLabel": "用户名", "usernameMinLength": "用户名至少需要2个字符", "usernamePlaceholder": "输入用户名", "verifying": "正在验证身份..."}, "misc": {"addUser": "添加用户", "confirmDelete": "确认删除", "delete": "删除", "edit": "编辑", "unassigned": "未指定", "view": "查看"}, "models": {"addLLM": "添加LLM", "apiKeyPlaceholder": "输入API Key", "apiKeyRequired": "API Key 不能为空", "apiVersionRequired": "API版本不能为空", "baseUrlPlaceholder": "输入基础URL", "baseUrlRequired": "基础URL不能为空", "config": "模型配置", "configFail": "模型配置失败，请检查输入并重试", "configSuccess": "模型配置成功", "configuredModels": "已配置的模型", "confirmDelete": "您确定要删除模型 \"{model}\" 吗？此操作无法撤销。", "deleteFail": "删除模型失败", "deleteModel": "删除模型", "deleteSuccess": "删除模型成功", "editConfig": "修改配置", "inputModelAlias": "请输入模型别名", "inputModelName": "请输入模型名称", "inputModelPurpose": "请输入模型用途", "inputModelType": "请输入模型类型", "maxTokensRequired": "请选择有效的最大Token数", "modelAliasRequired": "模型别名不能为空", "modelNameRequired": "模型名称不能为空", "modelPurposeRequired": "模型用途不能为空", "modelSettings": "模型设置", "modelTypeRequired": "模型类型不能为空", "selectModelPurpose": "选择模型用途", "selectModelType": "选择模型类型", "unconfiguredModels": "待配置的模型"}, "nav": {"dashboard": "仪表板", "models": "模型", "teams": "团队", "users": "用户"}, "password": {"changeDescription": "请输入当前密码和新密码以完成修改", "changeFail": "修改密码失败", "changeSuccess": "密码修改成功", "changeTitle": "修改超级管理员密码", "confirmChange": "确认修改", "confirmMinLength": "确认密码至少需要6个字符", "confirmNewPassword": "确认新密码", "confirmNewPasswordPlaceholder": "再次输入新密码", "minLength": "新密码至少需要6个字符", "mismatch": "新密码与确认密码不匹配", "newPassword": "新密码", "newPasswordPlaceholder": "输入新密码"}, "sidebar": {"changePassword": "修改密码", "logout": "退出登录", "model": "模型配置", "siteConfig": "站点配置", "team": "团队管理", "user": "账户管理"}, "site": {"configUpdateFail": "站点配置更新失败", "configUpdateSuccess": "站点配置更新成功", "description": "{name}是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。", "fileUploadFail": "文件上传失败", "fileUploadSuccess": "文件上传成功", "initLoginName": "请初始化管理端登录用户名", "initLoginPassword": "请初始化管理端登录密码", "inputSiteName": "请输入站点名称", "title": "{name} - 从知识和数据中快速获取洞察"}, "siteConfig": {"adminLoginPassword": "管理端登录密码", "adminLoginUsername": "管理端登录用户名", "cancel": "取消", "darkLogo": "Dark Logo", "darkLogoDescription": "建议尺寸：140px * 40px, 该图标显示在暗黑模式下的登录页中", "description": "配置站点名称、Logo和图标", "favicon": "Favicon", "faviconDescription": "建议尺寸：16px * 16px, 该图标显示在浏览器标签中", "logo": "Logo", "logoDescription": "建议尺寸：140px * 40px, 该图标显示在登录页中", "messages": {"updateFailed": "站点配置更新失败", "updateSuccess": "站点配置更新成功", "uploadFailed": "文件上传失败", "uploadSuccess": "文件上传成功"}, "miniDarkLogo": "Mini Dark Logo", "miniDarkLogoDescription": "建议尺寸：40px * 40px, 该图标显示在暗黑模式下的导航栏中", "miniLogo": "Mini Logo", "miniLogoDescription": "建议尺寸：40px * 40px, 该图标显示在导航栏中", "placeholders": {"adminLoginPassword": "请初始化管理端登录密码", "adminLoginUsername": "请输入管理端登录用户名", "siteName": "请输入站点名称"}, "save": "保存", "siteConfig": "站点配置", "siteName": "站点名称", "submitting": "提交中...", "upload": "上传", "uploadImage": "上传图片", "validation": {"adminLoginPasswordRequired": "管理端登录密码不能为空", "adminLoginUsernameRequired": "管理端登录用户名不能为空", "siteNameRequired": "站点名称不能为空"}}, "teams": {"admin": "管理员", "create": {"fail": {"tips": "创建团队失败"}}, "createTeam": "创建团队", "createdAt": "创建时间", "delete": {"confirm": "您确定要删除团队 \"{name}\" 吗？此操作无法撤销。", "fail": {"title": "删除团队失败"}}, "details": "团队详情", "editTeam": "编辑团队", "form": {"adminDescription": "团队管理员拥有管理团队成员的权限", "adminLabel": "团队管理员", "adminRequired": "请至少选择一个团队管理员", "allUsersLoaded": "已加载全部用户", "chatModelLabel": "聊天模型", "chatModelPlaceholder": "请选择适当的聊天模型", "chatModelRequired": "请选择适当的模型", "createButton": "创建团队", "embdModelLabel": "嵌入模型", "embdModelPlaceholder": "请选择适当的嵌入模型", "embdModelRequired": "请选择适当的模型", "loading": "加载中...", "loadingMore": "加载更多...", "loadingUsers": "加载用户失败", "nl2pythonModelLabel": "NL2PYTHON模型", "nl2pythonModelPlaceholder": "请选择适当的NL2PYTHON模型", "nl2pythonModelRequired": "请选择适当的模型", "nl2sqlModelLabel": "NL2SQL模型", "nl2sqlModelPlaceholder": "请选择适当的NL2SQL模型", "nl2sqlModelRequired": "请选择适当的模型", "noUsersFound": "没有找到用户", "rerankModelLabel": "<PERSON><PERSON>模型", "rerankModelPlaceholder": "请选择适当的Rerank模型", "rerankModelRequired": "请选择适当的模型", "saveButton": "保存修改", "saveTeamFailed": "保存团队失败", "scrollToLoadMore": "向下滚动加载更多", "searchUserPlaceholder": "搜索用户...", "searching": "搜索中...", "selectAdminPlaceholder": "选择团队管理员", "selectedAdminCount": "已选择 {count} 个管理员", "teamNameLabel": "团队名称", "teamNamePlaceholder": "输入团队名称", "teamNameRequired": "团队名称不能为空", "unknownUser": "用户-{id}"}, "member": "成员", "memberCount": "成员数量", "memberName": "成员名称", "name": "团队名称", "noTeamMembers": "暂无团队成员", "role": "角色", "searchUser": "搜索用户...", "selectAdmin": "选择团队管理员", "selectChatModel": "请选择适当的聊天模型", "selectEmbedModel": "请选择适当的嵌入模型", "selectNL2PythonModel": "请选择适当的NL2PYTHON模型", "selectNL2SQLModel": "请选择适当的NL2SQL模型", "selectRerankModel": "请选择适当的Rerank模型", "selectedAdminCount": "已选择 {count} 个管理员", "teamMembers": "团队成员", "unassigned": "未指定", "update": {"fail": {"title": "更新团队失败"}}}, "users": {"accountName": "账户名", "accountNameRequired": "账户名不能为空", "actions": "操作", "create": {"fail": {"title": "创建用户失败"}}, "createAccount": "创建账户", "createUser": "创建账户", "createdAt": "创建时间", "delete": {"confirm": "您确定要删除账户 \"{login_name}\" 吗？此操作无法撤销。", "fail": {"title": "删除用户失败"}}, "editUser": "编辑账户", "enterAccountName": "请输入账户名", "inputAccountName": "输入账户名", "inputPassword": "输入密码", "leaveEmptyForNoChange": "留空表示不修改密码", "loginName": "账户名", "manage": "账户管理", "modifyPassword": "修改密码", "noChangePasswordHint": "如果不需要修改密码，请留空", "passwordRequired": "密码至少需要6个字符", "saveChanges": "保存修改", "update": {"fail": {"title": "更新用户失败"}}}}}