"use client";

import { LLMFactory } from "@/common/model";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式
const formSchema = z.object({
  model: z.string().min(1, "模型配置不能为空"),
});

type FormValues = z.infer<typeof formSchema>;

interface ApiKeyModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormValues) => void;
  loading?: boolean;
  initialApiKey?: string;
  llmFactory?: string;
  title?: string;
}

const modelsWithBaseUrl = [LLMFactory.OpenAI, LLMFactory.AzureOpenAI];

export function Nl2SQLDialog({
  open,
  onClose,
  onSubmit,
  loading = false,
  initialApiKey = "",
  llmFactory,
  title = "模型设置",
}: ApiKeyModalProps) {
  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      model: initialApiKey,
    },
  });
  const t = useTranslations("management");

  const options = [{ value: "deepseek", label: "deepseek" }];

  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    onSubmit(values);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="text-red-500">*</span> 选择模型
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择模型类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {t("common.submitting")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
