"use client";

import { checkIsOwner, useIsTeamAdmin } from "@/lib/user-role";
import {
  AgentCreateParams,
  AgentDetailsParams,
  Agents,
  useAgentDetail,
  useUpdateAgent,
} from "@/service/agent-service";
import { useCreateSession } from "@/service/session-service";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu";
import { Separator } from "@ragtop-web/ui/components/separator";
import {
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@ragtop-web/ui/components/sidebar";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import {
  BotMessageSquare,
  BrushCleaning,
  ChevronRight,
  ListX,
  <PERSON>ader2,
  MessageSquarePlus,
  <PERSON>H<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>en,
  Trash2,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import { ClearConfirmDialog, DeleteConfirmDialog } from "../agent";
import { AgentDialog } from "../agent/agent-dialog";
import { SessionList } from "./session-list";

export function NavSessionItem({ agent }: { agent: Agents }) {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const t = useTranslations("web.agent");
  const [isAgentEditDialogOpen, setIsAgentEditDialogOpen] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<AgentDetailsParams | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());
  const [addSessionLoading, setAddSessionLoading] = useState(false);
  const [batchDeleteMode, setBatchDeleteMode] = useState<Set<string>>(new Set());

  const [isClearSessionDialogOpen, setIsClearSessionDialogOpen] = useState(false);

  // API hooks
  const agentDetail = useAgentDetail();
  const updateAgent = useUpdateAgent(currentAgent?.id || "");
  const createSession = useCreateSession();
  const isAdmin = useIsTeamAdmin();
  const isOwner = checkIsOwner(agent?.creator?.id, agent?.creator?.admin);
  const canEditAgent = agent?.scope === "PRIVATE" || isAdmin;
  const canDeleteAgent = agent?.scope === "PRIVATE" || (isOwner && isAdmin);

  // 检查Agent是否被选中
  const isAgentSelected = (agentId: string) => {
    return pathname.startsWith(`/agent/${agentId}`);
  };

  // 新增session
  const handleAddSession = async (agentId: string) => {
    if (addSessionLoading) {
      return;
    } // 防止重复点击
    setAddSessionLoading(true);
    try {
      await agentDetail.mutateAsync(
        { agent_id: agentId },
        {
          onSuccess: async (data) => {
            if (data.settings?.llm_model_name) {
              try {
                const sessionData = {
                  agent_id: agentId,
                  title: `新会话`,
                };

                const newSession = await createSession.mutateAsync(sessionData);

                toast({
                  title: "成功",
                  description: "会话创建成功",
                });

                // 导航到新会话
                router.push(`/agent/${agentId}/${newSession}`);
              } catch (error) {
                console.error("创建会话失败:", error);
                toast({
                  title: "错误",
                  description: "创建会话失败",
                  variant: "destructive",
                });
              }
            } else {
              toast({
                title: "错误",
                description: "Agent没有配置模型, 不能创建session",
                variant: "destructive",
              });
            }
          },
        }
      );
    } finally {
      setAddSessionLoading(false);
    }
  };

  // 处理编辑Agent
  const handleEditAgent = (agent: Agents) => {
    agentDetail.mutate(
      { agent_id: agent.id },
      {
        onSuccess: (data) => {
          setCurrentAgent(data);
          setIsAgentEditDialogOpen(true);
        },
      }
    );
  };

  // 处理更新Agent
  const handleUpdateAgent = async (data: AgentCreateParams) => {
    if (!currentAgent) {
      return;
    }

    try {
      await updateAgent.mutateAsync({ ...data, agent_id: currentAgent.id });
      toast({
        title: "成功",
        description: "Agent更新成功",
      });
      setIsAgentEditDialogOpen(false);
    } catch (error) {
      console.error("更新Agent失败:", error);
      toast({
        title: "错误",
        description: "更新Agent失败",
        variant: "destructive",
      });
    }
  };

  const handleOpenDeleteDialog = (agent: Agents) => {
    setCurrentAgent(agent);
    setIsDeleteDialogOpen(true);
    // 手动关闭 DropdownMenu
    setIsDropdownOpen(false);
  };

  // 处理批量删除Session
  const handleBatchDeleteSessions = (agentId: string) => {
    // 展开当前agent - 使用函数式更新确保状态正确更新
    setExpandedAgents((prev) => {
      const newExpanded = new Set(prev);

      newExpanded.add(agentId);

      return newExpanded;
    });

    // 进入批量删除模式
    setBatchDeleteMode((prev) => {
      const newBatchDeleteMode = new Set(prev);

      newBatchDeleteMode.add(agentId);

      return newBatchDeleteMode;
    });

    // 关闭下拉菜单
    setIsDropdownOpen(false);
  };

  return (
    <>
      <Collapsible
        key={agent.id}
        asChild
        className="group/collapsible"
        open={expandedAgents.has(agent.id)}
        onOpenChange={(open) => {
          if (open) {
            setExpandedAgents((prev) => {
              const newExpanded = new Set(prev);

              newExpanded.add(agent.id);

              return newExpanded;
            });
          } else {
            setExpandedAgents((prev) => {
              const newExpanded = new Set(prev);

              newExpanded.delete(agent.id);

              return newExpanded;
            });

            // 收起时取消批量删除模式
            setBatchDeleteMode((prev) => {
              const newBatchDeleteMode = new Set(prev);

              newBatchDeleteMode.delete(agent.id);

              return newBatchDeleteMode;
            });
          }
        }}
      >
        <SidebarMenuItem>
          <SidebarMenuButton asChild isActive={isAgentSelected(agent.id)}>
            <Link href={`/agent/${agent.id}`} prefetch={false}>
              <BotMessageSquare />
              <span>{agent.name}</span>
            </Link>
          </SidebarMenuButton>
          <CollapsibleTrigger asChild>
            <SidebarMenuAction
              className="bg-sidebar-accent text-sidebar-accent-foreground left-1 data-[state=open]:rotate-90"
              showOnHover
            >
              <ChevronRight className="mb-1" />
            </SidebarMenuAction>
          </CollapsibleTrigger>
          <SidebarMenuAction
            showOnHover
            onClick={() => handleAddSession(agent.id)}
            disabled={addSessionLoading}
            className="pr-2"
          >
            {addSessionLoading ? (
              <Loader2 className="mr-10 h-4 w-4 animate-spin" />
            ) : (
              <MessageSquarePlus className="mr-10 h-4 w-4" />
            )}
          </SidebarMenuAction>
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarMenuAction
                showOnHover
                onClick={() => {
                  router.push(`/agent/${agent.id}`);
                }}
              >
                <MoreHorizontal />
                <span className="sr-only">More</span>
              </SidebarMenuAction>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-48 rounded-lg"
              side={isMobile ? "bottom" : "right"}
              align={isMobile ? "end" : "start"}
            >
              <DropdownMenuItem
                onClick={() => handleEditAgent(agent as Agents)}
                disabled={!canEditAgent}
              >
                <SquarePen className="text-muted-foreground h-4 w-4" />
                <span className="text-xs">{t("actions.editAgent")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleOpenDeleteDialog(agent as Agents)}
                onSelect={(e) => e.preventDefault()}
                disabled={!canDeleteAgent}
              >
                <Trash2 className="text-muted-foreground h-4 w-4" />
                <span className="text-xs">{t("actions.deleteAgent")}</span>
              </DropdownMenuItem>

              <Separator className="my-2"></Separator>

              <DropdownMenuItem onClick={() => handleBatchDeleteSessions(agent.id)}>
                <ListX className="text-muted-foreground h-4 w-4" />
                <span className="text-xs">{t("actions.batchDeleteSessions")}</span>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => {
                  setIsClearSessionDialogOpen(true);
                  setIsDropdownOpen(false);
                }}
                onSelect={(e) => e.preventDefault()}
              >
                <BrushCleaning className="text-muted-foreground h-4 w-4" />
                <span className="text-xs">{t("actions.clearSessions")}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <CollapsibleContent className="w-full">
            <SessionList
              agentId={agent.id}
              isExpanded={expandedAgents.has(agent.id)}
              isBatchDeleteMode={batchDeleteMode.has(agent.id)}
              onCancelBatchDelete={() => {
                setBatchDeleteMode((prev) => {
                  const newBatchDeleteMode = new Set(prev);

                  newBatchDeleteMode.delete(agent.id);

                  return newBatchDeleteMode;
                });
              }}
            />
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
      {/* Agent 编辑对话框 */}
      {currentAgent && isAgentEditDialogOpen && (
        <AgentDialog
          open={isAgentEditDialogOpen}
          onClose={() => setIsAgentEditDialogOpen(false)}
          onSubmit={handleUpdateAgent}
          initialData={currentAgent}
        />
      )}
      {currentAgent && isDeleteDialogOpen && (
        <DeleteConfirmDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          title={t("delete.confirmTitle")}
          itemScope={currentAgent.scope}
          agentId={currentAgent.id}
        />
      )}

      {/* 清空Session对话框 */}
      <ClearConfirmDialog
        open={isClearSessionDialogOpen}
        onOpenChange={setIsClearSessionDialogOpen}
        itemType="session"
        agentId={agent.id}
        customDescription="清空session后，这个agent的所有会话和数据都将被永久删除。"
        onSuccess={() => {
          // 清空Session成功后的回调
        }}
      />
    </>
  );
}
