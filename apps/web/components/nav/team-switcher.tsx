"use client";

import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { ChevronsUpDown, UsersRound } from "lucide-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";

import { TeamType } from "@/service/team-service";
import {
  currentTeamAtom,
  currentUserAtom,
  setCurrentTeamAtom,
  teamListAtom,
} from "@/store/team-store";
import { siteConfigAtom } from "@/store/user-store";
import { Badge } from "@ragtop-web/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@ragtop-web/ui/components/sidebar";
import Image from "next/image";

const getRoles = (roles?: string[], t: (key: string) => string) => {
  if (roles?.includes("TEAM_ADMIN")) {
    return t("admin");
  } else {
    return t("member");
  }
};

export function TeamSwitcher() {
  const { isMobile } = useSidebar();
  const theme = useTheme();
  const isDark = theme.resolvedTheme === "dark";
  const siteConfig = useAtomValue(siteConfigAtom);
  const data = useAtomValue(teamListAtom);
  const currentTeam = useAtomValue(currentTeamAtom);
  const setTeam = useSetAtom(setCurrentTeamAtom);
  const currentUser = useAtomValue(currentUserAtom);
  const t = useTranslations("web.sidebar.team");

  const privateTeam = data.filter(({ type }) => type === TeamType.Personal);
  const generalTeam = data.filter(({ type }) => type === TeamType.General);

  // 如果没有团队数据或当前团队，不显示任何内容
  if (!data?.length || !currentTeam) {
    return null;
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              {siteConfig && (
                <Image
                  src={
                    isDark
                      ? `/frontend-api/image-proxy?key=${siteConfig?.mini_dark_logo}`
                      : `/frontend-api/image-proxy?key=${siteConfig?.mini_logo}`
                  }
                  alt="logo"
                  width={32}
                  height={32}
                  unoptimized
                />
              )}
              {/* <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Image src={"/logo/mini-logo.svg"} alt="logo" width={16} height={16} />
                <UsersRound className="size-4" />
              </div> */}
              <div className="grid flex-1 text-left text-sm leading-tight">
                {currentTeam.type === TeamType.Personal ? (
                  <span className="truncate font-medium">
                    {currentUser?.username}
                    {t("personalSpaceSuffix")}
                  </span>
                ) : (
                  <>
                    <span className="truncate font-medium">{currentTeam.title}</span>
                    <Badge variant="secondary">{getRoles(currentTeam.roles, t)}</Badge>
                  </>
                )}
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              {t("personalSpace")}
            </DropdownMenuLabel>
            {privateTeam?.map((team) => (
              <DropdownMenuItem
                key={team.id}
                onClick={() => {
                  // 使用Jotai设置当前团队
                  setTeam(team);
                  // 刷新页面以更新权限
                  window.location.href = "/";
                }}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  <UsersRound className="size-3.5 shrink-0" />
                </div>
                {currentUser?.username}
                {t("spaceSuffix")}
              </DropdownMenuItem>
            ))}
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              {t("enterpriseSpace")}
            </DropdownMenuLabel>
            {generalTeam?.map((team) => (
              <DropdownMenuItem
                key={team.id}
                onClick={() => {
                  // 使用Jotai设置当前团队
                  setTeam(team);
                  // 刷新页面以更新权限
                  window.location.href = "/";
                }}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  <UsersRound className="size-3.5 shrink-0" />
                </div>
                {team.title}
                <DropdownMenuShortcut>{getRoles(team.roles, t)}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
