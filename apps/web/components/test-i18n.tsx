"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";
import { useTranslations } from "next-intl";

/**
 * i18n-ally 测试组件
 * 
 * 用于测试 i18n-ally 插件的功能，包括：
 * - 翻译键的跳转
 * - 翻译内容的预览
 * - 翻译键的自动补全
 */
export function TestI18nComponent() {
  const t = useTranslations("web.test");

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
        <CardDescription>{t("description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="p-4 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground mb-2">
            {t("instructions")}
          </p>
          <p className="text-sm">
            {t("note")}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="default">
            {t("button.save")}
          </Button>
          <Button variant="outline">
            {t("button.cancel")}
          </Button>
        </div>
        
        {/* 测试嵌套键 */}
        <div className="mt-4 p-3 border rounded">
          <h4 className="font-medium mb-2">测试嵌套翻译键：</h4>
          <ul className="text-sm space-y-1">
            <li>• {t("button.save")}</li>
            <li>• {t("button.cancel")}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
