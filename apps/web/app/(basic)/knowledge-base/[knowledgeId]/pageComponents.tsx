"use client";
import { FileSelectionDrawer } from "@/app/(basic)/knowledge-base/components/file-selection-drawer";
import { getFileIcon } from "@/lib/file-utils";
import { checkIsOwner } from "@/lib/user-role";
import { formatDateTime } from "@/lib/utils";
import {
  type KnowledgeBaseDocList,
  RunStatus,
  useKBaseDocDelete,
  useKBaseDocDisabled,
  useKBaseDocEnable,
  useKBaseDocModifyChunking,
  useKBaseDocStartParse,
  useKBaseDocStopParse,
  useKnowledgeBaseDoc,
} from "@/service/knowledge-base-doc-service";
import {
  SliceMethodType,
  useAddKnowledgeBaseDocuments,
  useKnowledgeBase,
} from "@/service/knowledge-base-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import { Button } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { type ColumnDef, createStickyColumn } from "@ragtop-web/ui/components/data-table";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table";
import { Switch } from "@ragtop-web/ui/components/switch";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination";
import { useBatchOperations, useTableSelection } from "@ragtop-web/ui/hooks/use-table-selection";
import { Loader2, Play, PlusIcon, RefreshCw, Settings, Square, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { type ReactElement, useEffect, useState } from "react";
import { truncateFileName } from "../../files/utils";
import { ChunkMethodModal } from "../components/chunk-method-modal";
import { sliceMethodOptions } from "../components/slice-method-description";

// 解析状态类型
export type ParseStatus = RunStatus;

// 渲染状态图标
function renderStatusBadge(
  status: string,
  progress: number,
  statusT: (key: string) => string
): ReactElement {
  const percent = `${(progress * 100).toFixed(2)}%`;
  const queueing = progress < 0.01;

  switch (status) {
    case RunStatus.Done:
      return (
        <Badge variant="default" className="border-green-200 bg-green-50 text-green-700">
          {statusT("status.parseSuccess")}
        </Badge>
      );
    case RunStatus.Fail:
      return <Badge variant="destructive">{statusT("status.parseFailed")}</Badge>;
    case RunStatus.Running:
      return (
        <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-700">
          {queueing ? statusT("status.queueing") : `${statusT("status.parsing")} (${percent})`}
        </Badge>
      );
    case RunStatus.Unstart:
      return <Badge variant="outline">{statusT("status.unparsed")}</Badge>;
    case RunStatus.Cancelling:
      return <Badge variant="outline">{statusT("status.cancelling")}</Badge>;
    case RunStatus.Cancel:
      return <Badge variant="outline">{statusT("status.cancelled")}</Badge>;
    default:
      return <Badge variant="outline">{statusT("status.unknown")}</Badge>;
  }
}

export default function KnowledgeBaseDetails({ knowledgeId }: { knowledgeId: string }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();
  const t = useTranslations("web.knowledgeBase.details");
  const statusT = useTranslations("web.knowledgeBase");
  // 切片方法设置模态框状态
  const [isChunkMethodModalOpen, setIsChunkMethodModalOpen] = useState(false);
  const [selectedFileForChunkMethod, setSelectedFileForChunkMethod] =
    useState<KnowledgeBaseDocList | null>(null);

  // 删除确认对话框状态
  const [fileToDelete, setFileToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // 批量删除确认对话框状态
  const [batchDeleteConfirm, setBatchDeleteConfirm] = useState<{
    fileIds: string[];
    count: number;
  } | null>(null);

  // 文件选择抽屉状态
  const [isFileSelectionDrawerOpen, setIsFileSelectionDrawerOpen] = useState(false);

  // 添加状态来跟踪当前正在操作的文件ID
  const [operatingFileId, setOperatingFileId] = useState<string | null>(null);

  // 选择状态管理
  const selection = useTableSelection<KnowledgeBaseDocList>();

  const batchOps = useBatchOperations(selection.selectedRows);

  const {
    pageNumber,
    pageSize,
    handlePageChange: originalHandlePageChange,
    handlePageSizeChange: originalHandlePageSizeChange,
  } = usePagination({
    initialPageSize: 10,
  });

  // 自定义页面切换处理函数，切换时清空选择状态
  const handlePageChange = (page: number) => {
    selection.clearSelection();
    originalHandlePageChange(page);
  };

  // 自定义页面大小切换处理函数，切换时清空选择状态
  const handlePageSizeChange = (size: number) => {
    selection.clearSelection();
    originalHandlePageSizeChange(size);
  };

  // Toast提示
  const { toast } = useToast();

  // 获取知识库数据 - 延迟加载
  const { data, isLoading } = useKnowledgeBaseDoc(knowledgeId, pageNumber, pageSize, isInitialized);
  const { data: knowledgeBase, isError } = useKnowledgeBase(knowledgeId, isInitialized);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  // API hooks
  const enableDocMutation = useKBaseDocEnable();
  const disableDocMutation = useKBaseDocDisabled();
  const startParseMutation = useKBaseDocStartParse();
  const stopParseMutation = useKBaseDocStopParse();
  const modifyChunkingMutation = useKBaseDocModifyChunking();
  const addDocumentsMutation = useAddKnowledgeBaseDocuments();
  const deleteDocumentsMutation = useKBaseDocDelete();

  const getChunkLabel = (value?: SliceMethodType) => {
    return sliceMethodOptions.find((opt) => opt.value === value)?.label || t("notSet");
  };

  // 处理批量启用/禁用
  const handleBatchParse = async (enable: boolean) => {
    await batchOps.executeBatch(async (rows) => {
      if (enable) {
        const enableStart = rows.reduce((acc, row) => {
          if (![RunStatus.Running, RunStatus.Cancelling].includes(row.run_status)) {
            acc.push(row.id);
          }

          return acc;
        }, [] as string[]);

        if (enableStart.length === 0) {
          toast({
            title: t("errorTitle"),
            description: t("noFilesToParse"),
            variant: "destructive",
          });

          return;
        }

        return startParseMutation.mutate(
          {
            document_ids: enableStart,
          },
          {
            onSuccess: () => {
              toast({
                title: t("successTitle"),
                description: t("filesInParseQueue", {
                  count: enableStart?.length,
                }),
              });
              selection.clearSelection();
            },
            onError: () => {
              toast({
                title: t("errorTitle"),
                description: t("batchOperationFailed"),
                variant: "destructive",
              });
            },
          }
        );
      } else {
        const enableStop = rows.reduce((acc, row) => {
          if ([RunStatus.Running].includes(row.run_status)) {
            acc.push(row.id);
          }

          return acc;
        }, [] as string[]);

        if (enableStop.length === 0) {
          toast({
            title: t("errorTitle"),
            description: t("noFilesToStop"),
            variant: "destructive",
          });

          return;
        }

        return stopParseMutation.mutate(
          {
            document_ids: enableStop,
          },
          {
            onSuccess: () => {
              toast({
                title: t("successTitle"),
                description: t("batchStopSuccess", {
                  count: enableStop?.length,
                }),
              });
              selection.clearSelection();
            },
            onError: () => {
              toast({
                title: t("errorTitle"),
                description: t("batchOperationFailed"),
                variant: "destructive",
              });
            },
          }
        );
      }
    });
  };

  // 处理添加新文档
  const handleAddNewDocument = () => {
    setIsFileSelectionDrawerOpen(true);
  };

  // 处理文件选择确认
  const handleFileSelectionConfirm = async (selectedFileIds: string[]) => {
    if (!selectedFileIds.length) {
      return;
    }

    try {
      await addDocumentsMutation.mutateAsync({
        kbase_id: knowledgeId,
        file_ids: selectedFileIds,
      });

      toast({
        title: t("successTitle"),
        description: t("addFilesSuccess", { count: selectedFileIds.length }),
      });

      setIsFileSelectionDrawerOpen(false);
    } catch {
      toast({
        title: t("errorTitle"),
        description: t("addFilesFailed"),
        variant: "destructive",
      });
    }
  };

  // 处理解析文件
  const handleParseFile = async (fileId: string, currentStatus: ParseStatus) => {
    if (!knowledgeBase) {
      return;
    }

    // 设置当前操作的文件ID
    setOperatingFileId(fileId);

    try {
      // 根据当前状态决定操作
      if (currentStatus === RunStatus.Running) {
        // 停止解析
        await stopParseMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_ids: [fileId],
        });
        toast({
          title: t("successTitle"),
          description: t("stopParseSuccess"),
        });
      } else {
        // 开始解析
        await startParseMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_ids: [fileId],
        });
        toast({
          title: t("successTitle"),
          description: t("startParseSuccess"),
        });
      }
    } catch {
      toast({
        title: t("errorTitle"),
        description: t("operationFailed"),
        variant: "destructive",
      });
    } finally {
      // 操作完成后清除操作状态
      setOperatingFileId(null);
    }
  };

  // 处理切换文件启用状态
  const handleToggleEnabled = async (fileId: string, currentEnabled: boolean) => {
    if (!knowledgeBase) {
      return;
    }

    try {
      if (currentEnabled) {
        // 禁用文档
        await disableDocMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId,
        });
        toast({
          title: t("successTitle"),
          description: t("documentDisabled"),
        });
      } else {
        // 启用文档
        await enableDocMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId,
        });
        toast({
          title: t("successTitle"),
          description: t("documentEnabled"),
        });
      }
    } catch {
      toast({
        title: t("errorTitle"),
        description: t("operationFailed"),
        variant: "destructive",
      });
    }
  };

  // 处理设置切片方法
  const handleSetChunkMethod = (file: KnowledgeBaseDocList) => {
    setSelectedFileForChunkMethod(file);
    setIsChunkMethodModalOpen(true);
  };

  // 处理保存切片方法
  const handleSaveChunkMethod = async (values: {
    sliceMethod: string;
    chunk_token_num?: number;
    fileId: string;
  }) => {
    if (!selectedFileForChunkMethod) {
      return;
    }
    try {
      modifyChunkingMutation.mutate(
        {
          chunk_config: {
            chunk_provider_id: values.sliceMethod,
            chunk_token_num: values?.chunk_token_num,
            pdf_parser_config: {
              parser_provider_id: "DeepDOC",
            },
          },
          document_id: values.fileId,
        },
        {
          onSuccess: () => {
            toast({
              title: t("successTitle"),
              description: t("chunkMethodSetSuccess"),
            });
          },
        }
      );
    } catch {
      toast({
        title: t("errorTitle"),
        description: t("chunkMethodSetFailed"),
        variant: "destructive",
      });
    }
    setSelectedFileForChunkMethod(null);
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selection.selectedRows.length === 0) {
      return;
    }
    const selectedRows = selection.selectedRows.filter((row) =>
      checkIsOwner(row.creator?.id, row.creator?.admin)
    );

    if (selectedRows.length === 0) {
      toast({
        title: t("errorTitle"),
        description: t("noPermissionBatchDelete"),
        variant: "destructive",
      });

      return;
    }
    setBatchDeleteConfirm({
      fileIds: selectedRows.map((row) => row.id),
      count: selection.selectedRows.length,
    });
  };

  // 确认批量删除
  const confirmBatchDelete = () => {
    if (!batchDeleteConfirm) {
      return;
    }
    deleteDocumentsMutation.mutate(
      { document_ids: batchDeleteConfirm.fileIds },
      {
        onSuccess: () => {
          toast({
            title: t("successTitle"),
            description: t("batchDeleteSuccess", {
              count: batchDeleteConfirm.count,
            }),
          });
          selection.clearSelection();
          setBatchDeleteConfirm(null);
        },
        onError: () => {
          toast({
            title: t("errorTitle"),
            description: t("batchDeleteFailed"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理删除文件
  const handleDeleteFile = (fileId: string, fileName: string) => {
    setFileToDelete({ id: fileId, name: fileName });
  };

  // 确认删除文件
  const confirmDeleteFile = () => {
    if (!fileToDelete) {
      return;
    }
    deleteDocumentsMutation.mutate(
      {
        document_ids: [fileToDelete.id],
      },
      {
        onSuccess: () => {
          toast({
            title: t("successTitle"),
            description: t("deleteFileSuccess"),
          });
          setFileToDelete(null);
        },
        onError: () => {
          toast({
            title: t("errorTitle"),
            description: t("deleteFileFailed"),
            variant: "destructive",
          });
        },
      }
    );
  };

  if (isError) {
    return (
      <CustomContainer title={t("title")}>
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">{t("notFound")}</p>
        </div>
      </CustomContainer>
    );
  }

  if (!knowledgeBase) {
    return (
      <CustomContainer title={t("title")}>
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">{t("loading")}</p>
        </div>
      </CustomContainer>
    );
  }

  // 构建面包屑导航
  const breadcrumbs = [
    {
      title: t("backToList"),
      href: "/knowledge-base",
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: knowledgeBase.name || t("title"),
      href: `/knowledge-base/${knowledgeId}`,
      isCurrent: true,
    },
  ];

  const columns: ColumnDef<KnowledgeBaseDocList>[] = [
    createStickyColumn(
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      "left",
      50
    ),
    createStickyColumn(
      {
        accessorKey: "name",
        header: t("fileName"),
        cell: ({ row }) => (
          <div className="flex w-full items-center gap-2 overflow-hidden font-medium">
            <div className="flex-shrink-0">{getFileIcon(row.original?.type || "")}</div>
            <span className="truncate">{truncateFileName(row.original.name, 25)}</span>
          </div>
        ),
      },
      "left",
      300
    ),
    {
      accessorKey: "chunk_num",
      header: t("chunkCount"),
    },

    {
      accessorKey: "sliceMethod",
      header: t("sliceMethod"),
      cell: ({ row }) => {
        const file = row?.original;
        const method = file.chunk_config?.chunk_provider_id;
        const option = sliceMethodOptions.find((opt) => opt.value === method);

        return option ? option.label : method || t("notSet");
      },
    },
    {
      accessorKey: "chunk_token_num",
      header: t("chunkSize"),
      cell: ({ row }) => row.original.chunk_config?.chunk_token_num,
    },
    {
      accessorKey: "enable_status",
      header: t("enabled"),
      cell: ({ row }) => {
        const file = row.original;
        const isEnabled = file.enable_status === "ENABLED";

        return (
          <Switch
            disabled={[RunStatus.Running, RunStatus.Cancelling].includes(file.run_status)}
            checked={isEnabled}
            onCheckedChange={() => handleToggleEnabled(file.id, isEnabled)}
          />
        );
      },
    },

    {
      accessorKey: "run_status",
      header: t("status"),
      cell: ({ row }) => renderStatusBadge(row.original.run_status, row.original.progress, statusT),
    },
    {
      accessorKey: "owner_name",
      header: t("createdBy"),
      cell: ({ row }) => row.original.creator?.name,
    },
    {
      accessorKey: "create_time",
      header: t("createTime2"),
      cell: ({ row }) => formatDateTime(row?.original?.create_time || ""),
    },
    {
      accessorKey: "update_name",
      header: t("updatedBy"),
      cell: ({ row }) => row.original.updator?.name,
    },
    {
      accessorKey: "update_time",
      header: t("updateTime2"),
      cell: ({ row }) => formatDateTime(row?.original?.update_time || ""),
    },

    createStickyColumn(
      {
        id: "actions",
        header: () => <div className="text-right">{t("actions")}</div>,
        cell: ({ row }) => {
          const file = row.original;
          const isOwner = checkIsOwner(file.creator?.id, file.creator?.admin);
          const parseTooltip = () => {
            if (file.run_status === RunStatus.Cancelling) {
              return t("cannotParseWhileCancelling");
            }
            if (file.run_status === RunStatus.Running) {
              return t("stopParse");
            }
            if (file.run_status === RunStatus.Fail) {
              return t("retryParse");
            }

            return t("parseFile");
          };

          return (
            <div className="flex items-center justify-end gap-1">
              {/* 解析按钮 */}
              <IconButton
                variant="ghost"
                onClick={() => handleParseFile(file.id, file.run_status)}
                tooltip={parseTooltip()}
                disabled={file.run_status === RunStatus.Cancelling}
              >
                {operatingFileId === file.id &&
                (startParseMutation.isPending || stopParseMutation.isPending) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    {file.run_status === RunStatus.Running ? (
                      <Square className="h-4 w-4 text-amber-500" />
                    ) : file.run_status === RunStatus.Fail ? (
                      <RefreshCw className="text-destructive h-4 w-4" />
                    ) : file.run_status === RunStatus.Cancelling ? (
                      <Square className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Play className="text-primary h-4 w-4" />
                    )}
                  </>
                )}
              </IconButton>

              {/* 切片方法设置按钮 */}
              <IconButton
                variant="ghost"
                onClick={() => handleSetChunkMethod(file)}
                tooltip={
                  [RunStatus.Running, RunStatus.Cancelling].includes(file.run_status)
                    ? t("cannotSetChunkMethod")
                    : t("setChunkMethod")
                }
                disabled={[RunStatus.Running, RunStatus.Cancelling].includes(file.run_status)}
              >
                <Settings className="h-4 w-4" />
              </IconButton>

              {/* 删除按钮 */}
              <IconButton
                variant="ghost"
                onClick={() => handleDeleteFile(file.id, file.name)}
                tooltip={isOwner ? t("deleteFile") : t("noPermissionDelete")}
                disabled={!isOwner}
              >
                <Trash2 className="text-destructive h-4 w-4" />
              </IconButton>
            </div>
          );
        },
      },
      "right",
      160
    ),
  ];

  return (
    <CustomContainer
      title={knowledgeBase?.name || t("title")}
      breadcrumbs={breadcrumbs}
      action={
        <div className="flex items-center gap-2">
          {/* 批量操作按钮 */}

          <div className="mr-4 flex items-center gap-2">
            <span className="text-muted-foreground text-sm">
              {t("selectedFilesCount", {
                count: selection.selectedRows.length,
              })}
            </span>
            <Button
              size="sm"
              variant="outline"
              disabled={
                (startParseMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
              onClick={() => handleBatchParse(true)}
            >
              {startParseMutation.isPending && !operatingFileId ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                t("batchParse")
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              disabled={
                (stopParseMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
              onClick={() => handleBatchParse(false)}
            >
              {stopParseMutation.isPending && !operatingFileId ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                t("batchStop")
              )}
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={handleBatchDelete}
              disabled={
                (deleteDocumentsMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
            >
              {t("batchDelete")}
            </Button>
          </div>

          <Button size="sm" onClick={handleAddNewDocument}>
            <PlusIcon className="h-4 w-4" />
            {t("addFiles")}
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 知识库信息 */}
        <div className="text-muted-foreground bg-muted/30 mb-6 flex items-center gap-6 rounded-lg p-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="font-medium">{t("createTime")}</span>
            {formatDateTime(knowledgeBase?.create_time || "")}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">{t("defaultSliceMethod")}</span>
            {getChunkLabel(knowledgeBase?.chunk_config?.chunk_provider_id)}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">{t("documentCount")}</span>
            {knowledgeBase?.doc_num}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">{t("creator")}</span>
            {knowledgeBase?.creator?.name}
          </div>
        </div>
        <div className="no-scrollbar w-full">
          <PaginatedTable
            columns={columns}
            data={data}
            isLoading={isLoading}
            currentPage={pageNumber}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            showTotal={true}
            showPageSizeSelector={true}
            enableSelection={true}
            selectedRowIds={selection.selectedRowIds}
            onSelectionChange={selection.handleSelectionChange}
            onCurrentPageSelectionChange={selection.handleCurrentPageSelectionChange}
            className="no-scrollbar w-full"
            tableContainerClassName="overflow-x-auto border max-w-full no-scrollbar"
            tableClassName="min-w-[1400px]"
          />
        </div>
      </div>

      {/* 切片方法设置模态框 */}
      {isChunkMethodModalOpen && selectedFileForChunkMethod && (
        <ChunkMethodModal
          open={isChunkMethodModalOpen}
          onClose={() => setIsChunkMethodModalOpen(false)}
          onSubmit={handleSaveChunkMethod}
          file={selectedFileForChunkMethod}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={!!fileToDelete}
        onOpenChange={(open) => !open && setFileToDelete(null)}
        onConfirm={confirmDeleteFile}
        title={t("deleteDialog.title")}
        description={t("deleteDialog.singleFile", {
          fileName: fileToDelete?.name,
        })}
      />

      {/* 批量删除确认对话框 */}
      <ConfirmDialog
        open={!!batchDeleteConfirm}
        onOpenChange={(open) => !open && setBatchDeleteConfirm(null)}
        onConfirm={confirmBatchDelete}
        title={t("deleteDialog.title")}
        description={t("deleteDialog.multipleFiles", {
          count: batchDeleteConfirm?.count,
        })}
      />

      {/* 文件选择抽屉 */}
      {isFileSelectionDrawerOpen && knowledgeBase && (
        <FileSelectionDrawer
          open={isFileSelectionDrawerOpen}
          onClose={() => setIsFileSelectionDrawerOpen(false)}
          onConfirm={handleFileSelectionConfirm}
          kbaseId={knowledgeId}
          knowledgeBaseName={knowledgeBase.name || t("defaultKnowledgeBaseName")}
        />
      )}
    </CustomContainer>
  );
}
