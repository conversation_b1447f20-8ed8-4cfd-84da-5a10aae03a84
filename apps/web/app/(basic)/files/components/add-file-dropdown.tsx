"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu";
import { FolderPlus, Plus, Upload } from "lucide-react";
import { useTranslations } from "next-intl";

interface AddFileDropdownProps {
  onUploadFile: () => void;
  onCreateFolder: () => void;
  isInFolder?: boolean; // 是否在文件夹内，如果是则只显示上传文件按钮
}

/**
 * 新增文件下拉菜单组件
 */
export function AddFileDropdown({
  onUploadFile,
  onCreateFolder,
  isInFolder = false,
}: AddFileDropdownProps) {
  const t = useTranslations("web.files.actions");

  // 如果在文件夹内，只显示上传文件按钮
  if (isInFolder) {
    return (
      <Button size="sm" onClick={onUploadFile}>
        <Upload className="h-4 w-4" />
        {t("uploadFile")}
      </Button>
    );
  }

  // 在根目录显示下拉菜单
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="sm">
          <Plus className="h-4 w-4" />
          {t("newItem")}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={onUploadFile}>
          <Upload className="h-4 w-4" />
          {t("uploadFile")}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onCreateFolder}>
          <FolderPlus className="h-4 w-4" />
          {t("createFolder")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
