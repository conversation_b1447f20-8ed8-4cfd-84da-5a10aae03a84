#!/usr/bin/env node

/**
 * i18n-ally 重置脚本
 * 
 * 用于重置 i18n-ally 插件的缓存和配置
 */

import { execSync } from 'child_process';
import { existsSync, rmSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('🔄 重置 i18n-ally 配置...\n');

// 清理可能的缓存目录
const cacheDirectories = [
  '.vscode/.i18n-ally',
  'node_modules/.cache/i18n-ally',
  '.cache/i18n-ally'
];

console.log('🗑️  清理缓存目录:');
cacheDirectories.forEach(dir => {
  const fullPath = join(rootDir, dir);
  if (existsSync(fullPath)) {
    try {
      rmSync(fullPath, { recursive: true, force: true });
      console.log(`  ✅ 已删除 ${dir}`);
    } catch (error) {
      console.log(`  ❌ 删除 ${dir} 失败: ${error.message}`);
    }
  } else {
    console.log(`  ℹ️  ${dir} 不存在`);
  }
});

console.log('\n📋 当前配置摘要:');
console.log('  - 翻译文件路径: packages/i18n/dictionaries');
console.log('  - 支持语言: en, zh');
console.log('  - 源语言: en');
console.log('  - 显示语言: zh');
console.log('  - 框架: next-intl');

console.log('\n✨ 重置完成!');
console.log('\n🔧 下一步操作:');
console.log('1. 重启 VSCode');
console.log('2. 确保安装了 i18n-ally 扩展');
console.log('3. 打开任意包含翻译键的文件');
console.log('4. 检查是否显示翻译预览');
console.log('5. 尝试点击翻译键跳转到翻译文件');
