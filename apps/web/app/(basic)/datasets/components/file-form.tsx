"use client";

import { type Dataset } from "@/service/dataset-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { z } from "zod";

interface DatasetFormProps {
  dataset?: Dataset;
  onSave: (formData: FormValues) => void;
  isCreating: boolean;
  isLoading: boolean;
}

export function FilesetForm({ dataset, onSave, isLoading }: DatasetFormProps) {
  const t = useTranslations("web.datasets.fileForm");

  // 表单验证模式
  const formSchema = z.object({
    name: z.string().min(1, t("nameRequired")),
    description: z.string().min(1, t("descriptionRequired")),
  });

  type FormValues = z.infer<typeof formSchema>;

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: dataset
      ? {
          name: dataset.name || "",
          description: dataset.description || "",
        }
      : {
          name: "",
          description: "",
        },
  });

  // 保存数据集
  const handleSubmit = async () => {
    const values = form.getValues();

    try {
      onSave(values);
    } catch (e) {
      console.log(e);
    }
  };

  const onInvalid = (errors: any) => console.error(errors);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit, onInvalid)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("name")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={t("namePlaceholder")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("description")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={t("descriptionPlaceholder")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <p className="text-muted-foreground text-sm">{t("supportInfo")}</p>
          <span className="text-muted-foreground text-sm">{t("exampleLabel")}</span>
          <Image src="/template/csv-template.png" alt="excel" width={410} height={350} />
        </div>

        <div className="flex gap-3">
          <Button type="submit" className="flex-1" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("save")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
