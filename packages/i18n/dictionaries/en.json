{"web": {"agent": {"actions": {"batchDeleteSessions": "<PERSON><PERSON> Delete Sessions", "clearSessions": "Clear Sessions", "delete": "Delete", "deleteAgent": "Delete Agent", "edit": "Edit", "editAgent": "Edit Agent"}, "batchDelete": {"cancelButton": "Cancel", "cancelText": "Cancel", "confirmButton": "Confirm Delete", "confirmText": "Confirm Delete", "deleting": "Deleting...", "description": "Are you sure you want to delete the selected {count} sessions? This action cannot be undone, and all selected sessions will be permanently deleted.", "error": "Error", "failed": "Failed to batch delete sessions", "noSelection": "Please select sessions to delete", "success": "Successfully deleted {count} sessions", "title": "Confirm Batch Delete"}, "clear": {"cancelButton": "Cancel", "cancelText": "Cancel", "clearing": "Clearing...", "confirmButton": "Confirm Clear", "confirmText": "Confirm Clear", "description": "After clearing {itemType}, all related {itemType} will be permanently deleted.", "failed": "Failed to clear sessions", "success": "Sessions cleared successfully", "title": "Confirm Clear"}, "common": {"agentList": "Agent List", "assistant": "Assistant", "cancel": "Cancel", "chat": "Cha<PERSON>", "confirm": "Confirm", "error": "Error", "failed": "Failed", "loading": "Loading...", "loadingMessage": "Loading agent information...", "modelNotConfigured": "Agent model not configured, unable to chat. Please configure a model.", "noData": "No data available", "notFound": "Agent not found", "notFoundMessage": "Specified agent not found", "save": "Save", "success": "Success", "untitled": "Untitled Agent", "user": "User"}, "delete": {"cancelButton": "Cancel", "cancelText": "Cancel", "confirmButton": "Confirm Delete", "confirmDescription": "Are you sure you want to delete this agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "confirmText": "Delete", "confirmTitle": "Confirm Delete", "deleting": "Deleting...", "failed": "Failed to delete agent", "personalDescription": "Are you sure you want to delete this Agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "success": "Agent deleted successfully", "teamDescription": "Are you sure you want to delete this Agent? After deleting a team agent, related sessions and data of other users using this agent will also be permanently deleted."}, "details": {"actions": "Actions", "advancedSettings": "Advanced Settings", "agentDescription": "Agent Description", "agentName": "Agent Name", "agentPermission": "Agent Permission", "agentSettings": "Agent <PERSON>s", "assistantPrompt": "Assistant Prompt", "basicInfo": "Basic Information", "clearSessions": "Clear Sessions", "createTime": "Create Time", "creator": "Creator", "dataset": "Dataset", "deleteAgent": "Delete Agent", "description": "Description", "editAgent": "Edit Agent", "emptyReply": "Empty Reply", "exportSessions": "Export Sessions", "flexibility": "Flexibility", "greeting": "Greeting", "historyContext": "History Message Context (Count)", "keywordWeight": "Keyword Weight", "knowledgeBase": "Knowledge Base", "lastActiveTime": "Last Active Time", "maxTokens": "<PERSON>", "model": "Model", "modelConfig": "Model Configuration", "modelSettings": "Model Settings", "name": "Name", "noDescription": "No Description", "none": "None", "notConfigured": "Not Configured", "notSet": "Not Set", "notSetYet": "Not Set Yet", "permissions": {"personal": "Personal", "team": "Team"}, "promptSettings": "Prompt Settings", "resourceType": "Resource Type", "resourceTypes": {"databaseDataset": "Database Dataset", "fileDataset": "File Dataset", "knowledgeBase": "Knowledge Base"}, "retrievalCount": "Retrieval Count", "retrievalSettings": "Retrieval Settings", "sessionCount": "Session Count", "sessionManagement": "Session Management", "sessionSettings": "Session Settings", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "summaryPrompt": "Summary Prompt", "supportedLanguages": "Supported Languages", "systemPrompt": "System Prompt", "temperature": "Temperature", "topP": "Top P", "updateTime": "Update Time", "userPrompt": "User Prompt", "viewSessions": "View Sessions"}, "dialog": {"cancel": "Cancel", "confirm": "Confirm", "description": "Configure your dedicated assistant for your professional knowledge base here!", "save": "Save", "title": "Chat Configuration"}, "form": {"agentPermission": "Agent Permission", "assistantPrompt": "Assistant Prompt", "balanced": "Balanced", "chatModel": "Cha<PERSON>", "creative": "Creative", "database": "Database", "dataset": "Dataset", "datasetChatModel": "Dataset Chat", "datasets": "Datasets", "description": "Agent Description", "descriptionPlaceholder": "Enter a brief introduction about the Agent", "emptyReply": "Empty Reply", "emptyReplyPlaceholder": "e.g. Sorry, I cannot answer this question.", "file": "File", "flexibility": "Flexibility", "greeting": "Set Greeting", "greetingPlaceholder": "Hello! I am your assistant, how can I help you?", "historyContext": "History Message Context (Count)", "keywordWeight": "Keyword Similarity Weight", "knowledgeBase": "Knowledge Base", "maxTokens": "<PERSON>", "model": "Model", "modelNotConfiguredMessage": "Team has not configured {modelType} model, please contact administrator for configuration", "modelPlaceholder": "Please select a model", "name": "Agent Name", "namePlaceholder": "Test Agent", "personal": "Personal", "precise": "Precise", "prompt": "Prompt", "resourceType": "Resource Type", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "summaryPrompt": "Summary Prompt", "summaryPromptPlaceholder": "e.g. Please summarize the dataset based on the dataset content.", "supportedLanguages": "Supported Languages", "supportedLanguagesDescription": "Select the languages supported by the Agent, at least one language must be selected", "supportedLanguagesPlaceholder": "Please select supported languages", "systemPrompt": "System Prompt", "systemPromptPlaceholder": "e.g. You are an expert in the academic field, please answer questions in as much detail as possible based on the content of the knowledge base.", "team": "Team", "temperature": "Temperature", "topN": "Top N", "topP": "Top P", "userPrompt": "User Prompt", "validation": {"datasetRequired": "Please select dataset", "descriptionRequired": "Agent description cannot be empty", "knowledgeBaseRequired": "Please select knowledge base", "languageRequired": "Please select at least one language", "modelRequired": "Please select a model", "nameRequired": "Agent name cannot be empty", "permissionRequired": "Please select Agent permission", "promptRequired": "Prompt cannot be empty", "resourceRequired": "Please select resource", "resourceTypeRequired": "Please select resource type", "systemPromptRequired": "System prompt cannot be empty"}}, "helloMessage": "Hello! \nI am your assistant, is there anything that can help you?", "meta": {"createTime": "Created:", "creator": "Creator:", "updateTime": "Updated:", "updator": "Modified by:"}, "session": {"loadingSessionInfo": "Loading session information...", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "title": "Session"}, "tabs": {"basicSettings": "Basic Settings", "modelSettings": "Model Settings", "promptEngine": "Prompt Engine"}, "tips": {"createFailed": "Failed to create agent", "deleteFailed": "Failed to delete agent", "deleteSuccess": "Agent deleted successfully", "success": "Success", "updateFailed": "Failed to update agent", "updateSuccess": "Agent updated successfully"}}, "datasets": {"addDatabaseDataset": "Add Database Dataset", "addFileDataset": "Add File Dataset", "card": {"build": "Build", "building": "Building", "buildingVersion": "Building Version:", "createTime": "Create Time:", "creator": "Creator:", "currentVersion": "Current Data Version:", "database": "Database:", "delete": "Delete", "deleteDescription": "If the dataset is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "deleteTitle": "Confirm Delete", "description": "Description:", "edit": "Edit", "noPermissionDelete": "You don't have permission to delete", "none": "None"}, "comingSoon": "This feature is coming soon, please stay tuned~", "create": "Create Dataset", "databaseDataset": "Database Dataset", "databaseDetail": {"build": "Build", "buildError": "Error occurred while building dataset", "buildFailed": "Build Failed", "buildStarted": "Dataset build started", "building": "Building", "buildingVersion": "Building Version", "createTime": "Create Time", "creator": "Creator", "currentVersion": "Current Version", "database": "Database", "datasetList": "Dataset List", "error": "Error", "getColumnsError": "Failed to get table columns", "loading": "Loading...", "none": "None", "notFound": "Dataset not found", "schema": "<PERSON><PERSON><PERSON>", "selectTablePrompt": "Please select a table from the left to view column data", "title": "Dataset Details"}, "delete": {"description": "If the dataset is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteDialog": {"description": "This action cannot be undone.", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "title": "Confirm Delete"}, "emptyMessage": "No datasets yet, click the button above to create your first dataset", "file": {"worksheet": {"actions": "Actions", "cancel": "Cancel", "columnDescription": "Column Description", "columnDescriptionHelp": "Describe the meaning, purpose or related information of the column in detail", "columnDescriptionPlaceholder": "Please enter the column description...", "columnName": "List name", "columnNameHelp": "Provides a more understandable alias for this column", "columnNamePlaceholder": "Please enter the column name...", "dataType": "Data Type", "edit": "edit", "originalName": "Original file column name", "save": "confirm", "sheetAlias": "Sheet alias:", "sheetAliasHelp": "Provide a more understandable alias for this Sheet", "sheetAliasPlaceholder": "Please enter the <PERSON><PERSON> alias...", "sheetCommentHelp": "Describe the meaning, purpose or related information in detail", "sheetDescription": "Sheet Description:", "sheetDescriptionPlaceholder": "Please enter the Sheet description...", "sheetInfo": "Sheet information", "subSheetCount": "Number of subtables:", "tableName": "name", "unnamed": "Unnamed"}}, "fileDataset": "File Dataset", "fileDetail": {"actions": {"actions": "Actions", "batchOperationFailed": "Batch operation failed, please retry", "batchParseError": "No files available for parsing", "batchStopError": "No files available for stopping"}, "addFiles": "Add Files", "backToDatasets": "Dataset List", "batchDelete": "<PERSON><PERSON> Delete", "batchDeleteConfirm": "Are you sure you want to batch delete {count} files? This action cannot be undone.", "batchParse": "<PERSON><PERSON> Parse", "batchReset": "<PERSON><PERSON>", "batchStart": "<PERSON><PERSON> Start", "batchStop": "Batch Stop", "breadcrumb": "File Datasets", "buildDataset": "Build Dataset", "configMetadata": "Config <PERSON>", "configNote": "Data is cached locally during configuration, please do not refresh the page", "createTime": "Create Time:", "createTime2": "Create Time", "createdBy": "Created By", "creator": "Creator:", "deleteFile": "Delete File", "deleteFileConfirm": "Are you sure you want to delete file \"{fileName}\"? This action cannot be undone.", "documentCount": "Document Count:", "file": "File", "fileName": "File Name", "fileSize": "File Size", "hasUnsavedChanges": "Has unsaved changes", "loading": "Loading...", "messages": {"addFailed": "Add failed", "addFailedDesc": "Error occurred while adding files, please retry", "addSuccess": "Added successfully", "addSuccessDesc": "Successfully added {count} files to dataset", "batchDeleteFailed": "Error", "batchDeleteFailedDesc": "Batch delete failed, please retry", "batchDeleteSuccess": "Success", "batchDeleteSuccessDesc": "<PERSON><PERSON> deleted {count} files", "deleteFailed": "Error", "deleteFailedDesc": "Error occurred while deleting file, please retry", "deleteSuccess": "Success", "deleteSuccessDesc": "File deleted", "error": "Error", "noPermissionDelete": "Error", "noPermissionDeleteDesc": "You don't have permission to delete these files", "operationFailed": "Error", "operationFailedDesc": "Operation failed, please retry", "parseStartSuccess": "Success", "parseStartSuccessDesc": "File parsing started", "parseStopSuccess": "Success", "parseStopSuccessDesc": "File parsing stopped", "resetParseFailed": "Reset failed", "resetParseFailedDesc": "Error occurred while resetting parse, please retry", "resetParseSuccess": "Reset successful", "resetParseSuccessDesc": "File \"{fileName}\" parse has been reset", "success": "Success"}, "metadataConfig": "Metadata Configuration", "noPermissionConfig": "You don't have permission to configure", "noPermissionDelete": "You don't have permission to delete", "notFound": "File dataset not found", "parsing": "Parsing", "progress": "Progress", "resetParse": "Reset Parse", "resetParseConfirm": "After resetting the parse, the existing metadata configuration will also be reset. Are you sure you want to reset the parse for file \"{fileName}\"? This action cannot be undone.", "saveAndParse": "Save and Parse", "saveConfig": "Save Configuration", "saveFailed": "Save Failed", "saveFailedDesc": "An error occurred while saving metadata configuration", "saveSuccess": "Save Successful", "saveSuccessDesc": "Metadata configuration has been saved and parsing started", "selectWorksheetPrompt": "Please select a worksheet from the left to view content", "selectedFilesCount": "Selected {count} items", "startParse": "Start Parse", "status": "Status", "stopParse": "Stop Parse", "subtable": "Subtable", "title": "File Dataset Details", "tooltips": {"cannotResetParse": "Current status cannot reset parse", "deleteFile": "Delete file", "metadataNotParsed": "Not parsed successfully, cannot perform metadata settings", "metadataSettings": "Metadata settings", "noPermissionDelete": "You don't have permission to delete", "parseCancelling": "Cancelling, cannot perform parse operation", "parseFile": "Parse file", "parseRunning": "Parsing in progress, stopping parse is not supported", "resetParse": "Reset parse", "retryParse": "Retry parse"}, "updateTime": "Update Time", "updatedBy": "Updated By", "worksheet": "Worksheet", "worksheets": "Worksheets"}, "fileForm": {"create": "Create Dataset", "description": "Dataset Description", "descriptionPlaceholder": "Enter dataset description", "descriptionRequired": "Dataset description is required", "exampleLabel": "Example:", "name": "Dataset Name", "namePlaceholder": "Enter dataset name", "nameRequired": "Dataset name is required", "save": "Save", "supportInfo": "Only supports standard row-column structured CSV / EXCEL files. Complex format EXCEL files may fail to parse or parse successfully but query inaccurately", "update": "Update Dataset"}, "fileSelection": {"addToDataset": "Add to File Dataset", "cancel": "Cancel", "createTime": "Create time", "emptyText": "No matching files found", "endText": "All files loaded", "fileList": "File List", "filesCount": "{count} files", "loadingText": "Loading...", "maxFilesExceeded": "File dataset can associate maximum {max} files", "maxFilesLimit": "Maximum {max} files ({current}/{max})", "rootDirectory": "Root Directory", "searchPlaceholder": "Search files...", "selectedFilesCount": "Selected {count} files", "size": "Size", "title": "Select Files", "unlinkCount": "Unlinked count"}, "form": {"connectionError": "Connection error occurred, please try again later.", "connectionFailed": "Connection Failed", "connectionSuccess": "Connection successful! Database is accessible.", "create": "Create Database Dataset", "database": "Database Name", "databaseNote": "Only supports built-in analytical databases", "databaseRequired": "Database name is required", "description": "Dataset Description", "descriptionRequired": "Dataset description is required", "mustTestFirst": "Please test the connection successfully before saving", "name": "Dataset Name", "nameRequired": "Dataset name is required", "password": "Database Password", "passwordRequired": "Password is required", "testConnection": "Test Connection", "testing": "Testing...", "update": "Edit Dataset", "username": "Database Username", "usernameRequired": "Username is required"}, "loadError": "Failed to load dataset list: {error}", "loading": "Loading...", "messages": {"buildError": "Error occurred while building dataset", "buildFailed": "Build Failed", "buildStarted": "Dataset build started", "buildSuccess": "Building", "createError": "Error occurred while creating dataset", "createFailed": "Creation Failed", "createSuccess": "Created Successfully", "databaseDatasetDeleted": "Database dataset deleted successfully", "datasetCreated": "Dataset created", "datasetUpdated": "Dataset updated", "deleteDatabaseError": "Error occurred while deleting database dataset", "deleteFailed": "Deletion Failed", "deleteFileError": "Error occurred while deleting file dataset", "deleteSuccess": "Deleted Successfully", "fileDatasetDeleted": "File dataset deleted successfully", "updateError": "Error occurred while updating dataset", "updateFailed": "Update Failed", "updateSuccess": "Updated Successfully"}, "retry": "Retry", "saveChanges": "Save Changes", "schemaStructure": "Database structure", "tabs": {"databaseDataset": "Database Dataset", "fileDataset": "File Dataset"}, "title": "Dataset Management", "unknownError": "Unknown error"}, "fileManagement": {"actions": "Actions", "batchParse": "<PERSON><PERSON> Parse", "fileList": "File List", "fileName": "File Name", "filesCount": "{count} files", "noFilesFound": "No matching files found", "parseButton": {"parse": "Parse", "reparse": "Re-parse"}, "parseStatus": "Parse Status", "searchPlaceholder": "Search files...", "selectedFiles": "Selected {count} files", "status": {"failed": "Parse Failed", "none": "Not Parsed", "parsing": "Parsing", "success": "Parse Success"}, "title": "File Management"}, "files": {"actions": {"cancel": "Cancel", "clearFiles": "Clear", "confirm": "Confirm", "createFolder": "Create Folder", "deleteFile": "Delete File", "moveFile": "Move File", "newItem": "New", "noPermissionDelete": "You don't have permission to delete", "renameFile": "Rename File", "retry": "Retry", "retryUpload": "Retry Upload", "selectFiles": "Select Files", "startUpload": "Start Upload", "uploadFile": "Upload File"}, "dialog": {"cancel": "Cancel", "confirm": "Confirm", "create": "Create", "createFolderTitle": "Create Folder", "currentName": "Current name: {name}", "currentPath": "Current location: {path}", "deleteCancel": "Cancel", "deleteConfirm": "Confirm Delete", "deleteFileConfirm": "Are you sure you want to delete this file?", "deleteFolderConfirm": "Are you sure you want to delete this folder? Files in the folder may be linked to knowledge bases or file datasets. After deletion, all files and subfolders in this folder will be deleted, and the link relationships will also be cancelled. Are you sure you want to delete?", "deleteLinkedFileConfirm": "This file is currently linked to {linkNum} knowledge bases or file datasets. After deletion, the link relationships will also be cancelled. Are you sure you want to delete?", "deleteTitle": "Delete File", "folderName": "Folder Name", "folderNameInvalidChars": "Folder name cannot contain special characters \\ / : * ? \" < > |", "folderNameRequired": "Folder name cannot be empty", "moveTitle": "Move {type}", "nameInvalidChars": "Name cannot contain special characters \\ / : * ? \" < > |", "nameRequired": "Name cannot be empty", "newName": "New Name", "newNamePlaceholder": "Enter new {type} name", "renameFileTitle": "Rename {type}", "renamePlaceholder": "Enter new {type} name", "renameTitle": "Rename {type}", "types": {"file": "File", "folder": "Folder"}}, "emptyState": "No knowledge bases found, click the button above to create your first knowledge base", "loadError": "Failed to load file list, please refresh the page and try again", "loading": "Loading...", "messages": {"createFailed": "Failed to create knowledge base", "createFailedDesc": "Error occurred while creating knowledge base", "createFolderFailed": "Creation Failed", "createFolderFailedDesc": "Error occurred while creating folder", "createFolderSuccess": "Created Successfully", "createFolderSuccessDesc": "Folder {folderName} has been created", "createSuccess": "Knowledge base created successfully", "deleteFailed": "Deletion Failed", "deleteFailedDesc": "Error occurred while deleting", "deleteSuccess": "Deleted Successfully", "deleteSuccessDesc": "File has been deleted successfully", "filesSuccessCount": "{count} files uploaded successfully", "renameFolderFailed": "Rename Failed", "renameFolderFailedDesc": "Error occurred while renaming file", "renameFolderSuccess": "Renamed Successfully", "renameFolderSuccessDesc": "File has been renamed to {newName}", "updateFailed": "Failed to update knowledge base", "updateFailedDesc": "Error occurred while updating knowledge base", "updateSuccess": "Knowledge base updated successfully", "uploadSuccessCount": "Uploaded {count} files successfully"}, "pagination": {"nextPage": "Next Page", "pageInfo": "Page {current} of {total}", "previousPage": "Previous Page", "totalRecords": "(Total {count} records)"}, "status": {"uploadFailed": "Upload Failed", "uploadSuccess": "Upload Success", "uploading": "Uploading", "userCancel": "User Cancel", "waitingUpload": "Waiting Upload"}, "table": {"actions": "Actions", "createTime": "Create Time", "creator": "Creator", "linkedCount": "Linked Count", "name": "Name", "size": "Size", "type": "Type", "updateTime": "Update Time", "updater": "Updater"}, "title": "File Management", "types": {"file": "File", "folder": "Folder"}, "upload": {"clear": "Clear", "fileLimits": "Maximum {maxCount} files, excel, csv single file maximum {tableLimit}MB, other files single file maximum {fileLimit}MB", "retry": "Retry", "reupload": "Re-upload", "selectFiles": "Select Files", "selectedFiles": "Selected files ({count})", "startUpload": "Start Upload", "supportedFormats": "Supported formats: {formats}", "title": "Upload Files", "uploadFailed": "Upload Failed", "uploadProgress": "Uploading {percent}%", "uploadSuccess": "Upload Success", "uploadSuccessCount": "{count} files uploaded successfully", "uploading": "Uploading", "userCancelled": "User cancelled", "waitingUpload": "Waiting Upload"}}, "forms": {"placeholders": {"database": "Enter database", "datasetDescription": "Enter dataset description", "datasetName": "Enter dataset name", "description": "Enter description...", "folderName": "Enter folder name", "knowledgeBaseDescription": "Enter knowledge base description", "knowledgeBaseName": "Enter knowledge base name", "password": "Enter password", "search": "Search...", "searchFiles": "Search files...", "searchMembers": "Search members...", "username": "Enter username"}, "selectOptions": {"defaultSliceMethod": "Select default document slice method", "pdfParser": "Select PDF Parser", "sliceMethod": "Select slice method"}}, "home": {"adminDescription": " As a team administrator, you can manage team members and configure models.", "description": " is an AI service centered on personal and enterprise datasets, designed to unleash the full potential of data.", "userDescription": " You can access team-shared resources and create personal resources.", "welcomeBack": "Welcome Back", "welcomeTitle": "Welcome to {siteName}"}, "knowledgeBase": {"addKnowledgeBase": "Add Knowledge Base", "addNew": "Add New Knowledge Base", "card": {"creator": "Creator:", "delete": "Delete", "description": "Description:", "edit": "Edit", "fileCount": "File Count:", "filesCount": "{count} files", "noPermissionDelete": "You don't have permission to delete"}, "chunkMethod": {"cancel": "Cancel", "chunkSize": "Chunk Size", "chunkSizeMinError": "Chunk size cannot be less than 128", "currentMethod": "Current chunk method: {method}", "description": "Set chunk method for file {fileName}", "save": "Save", "selectNewMethod": "Select new chunk method", "selectSliceMethod": "Select slice method", "sliceMethod": "Slice Method", "sliceMethodDescription": "Choose the method for document slicing", "sliceMethodRequired": "Slice method cannot be empty", "title": "Chunk Method Settings"}, "create": "Create Knowledge Base", "delete": {"description": "If the knowledge base is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteDialog": {"description": "This action cannot be undone.", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "title": "Confirm Delete"}, "details": {"actions": "Actions", "addFiles": "Add Files", "addFilesFailed": "Failed to add files, please retry", "addFilesSuccess": "Successfully added {count} files to knowledge base", "backToList": "Knowledge Base List", "batchDelete": "<PERSON><PERSON> Delete", "batchDeleteFailed": "Batch delete failed", "batchDeleteSuccess": "Batch delete successful, {count} files deleted", "batchOperationFailed": "Batch operation failed, please retry", "batchParse": "<PERSON><PERSON> Parse", "batchStart": "<PERSON><PERSON> Start", "batchStop": "Batch Stop", "batchStopSuccess": "{count} files are being cancelled", "cannotParseWhileCancelling": "Cannot parse while cancelling", "cannotSetChunkMethod": "Cannot set chunk method in current state", "chunkCount": "Chunks", "chunkMethodSetFailed": "Failed to set chunk method, please retry", "chunkMethodSetSuccess": "Chunk method set successfully", "chunkSize": "Chunk Size", "clearSelection": "Clear Selection", "createTime": "Create Time", "createTime2": "Create Time", "createdBy": "Created By", "creator": "Creator", "defaultKnowledgeBaseName": "Knowledge Base", "defaultSliceMethod": "De<PERSON><PERSON>", "deleteDialog": {"description": "This action cannot be undone.", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "title": "Confirm Delete"}, "deleteFile": "Delete File", "deleteFileFailed": "Failed to delete file", "deleteFileSuccess": "File deleted successfully", "documentCount": "Document Count", "documentDisabled": "Document disabled", "documentEnabled": "Document enabled", "enabled": "Enabled", "errorTitle": "Error", "fileName": "Name", "fileSize": "File Size", "filesInParseQueue": "Added {count} files to parse queue", "loading": "Loading...", "loadingError": "Failed to load knowledge base details", "noFilesToParse": "No files to parse", "noFilesToStop": "No files currently parsing", "noPermissionBatchDelete": "You don't have permission to delete these files", "noPermissionDelete": "You don't have permission to delete", "noSelection": "Please select files to operate on first", "notFound": "Specified knowledge base not found", "notSet": "Not Set", "operationFailed": "Operation failed, please retry", "parseFile": "Parse File", "progress": "Progress", "retryParse": "Retry Parse", "selectAll": "Select All", "selectedFilesCount": "Selected {count} items", "setChunkMethod": "Set <PERSON> Method", "sliceMethod": "Slice Method", "startParseSuccess": "File parsing started", "status": "Status", "stopParse": "Stop Parse", "stopParseSuccess": "File parsing stopped", "successTitle": "Success", "title": "Knowledge Base Details", "updateTime": "Update Time", "updateTime2": "Update Time", "updatedBy": "Updated By"}, "edit": "Edit Knowledge Base", "emptyMessage": "No knowledge bases available, click the button above to create your first knowledge base", "fileSelection": {"addToKnowledgeBase": "Add to Knowledge Base", "breadcrumbHome": "Home", "cancel": "Cancel", "confirm": "Confirm Add", "createTime": "Create time", "description": "Please select files to add to the knowledge base", "emptyText": "No files found", "endText": "All files loaded", "fileList": "File List", "filesCount": "{count} files", "loadingText": "Loading...", "rootDirectory": "Root Directory", "searchPlaceholder": "Search files...", "selectFiles": "Select Files", "selectedCount": "Selected {count} files", "selectedFilesCount": "Selected {count} files", "size": "Size", "title": "Select files for knowledge base \"{name}\"", "unlinkCount": "Unlinked count"}, "form": {"defaultSliceMethod": "Default Document Slicing Method", "description": "Knowledge Base Description", "descriptionRequired": "Knowledge base description cannot be empty", "name": "Knowledge Base Name", "nameRequired": "Knowledge base name cannot be empty", "pdfParser": "PDF Parser", "sliceMethod": "Slice Method", "sliceMethodDescription": "Select the method for document slicing", "sliceMethodRequired": "Slicing method cannot be empty", "usesDeepDoc": "Use DeepDOC to parse PDF documents"}, "loadError": "Failed to load knowledge base list: {error}", "loading": "Loading...", "messages": {"addFilesFailed": "Failed to add files", "addFilesSuccess": "Files added successfully", "batchDeleteFailed": "Batch delete failed", "batchDeleteSuccess": "Batch delete successful", "batchStartFailed": "Batch start failed", "batchStartSuccess": "Batch start successful", "batchStopFailed": "Batch stop failed", "batchStopSuccess": "Batch stop successful", "createFailed": "Failed to create knowledge base", "createSuccess": "Knowledge base created successfully", "deleteFailed": "Failed to delete knowledge base", "deleteFileFailed": "Failed to delete file", "deleteFileSuccess": "File deleted successfully", "deleteSuccess": "Knowledge base deleted successfully", "startParseFailed": "Failed to start parsing", "startParseSuccess": "Parse task started", "stopParseFailed": "Failed to stop parsing", "stopParseSuccess": "Parse task stopped", "updateFailed": "Failed to update knowledge base", "updateSuccess": "Knowledge base updated successfully"}, "operations": {"delete": "Delete", "refresh": "Refresh", "settings": "Settings", "start": "Start", "stop": "Stop"}, "retry": "Retry", "saveChanges": "Save Changes", "status": {"cancelled": "<PERSON><PERSON>ed", "cancelling": "Cancelling", "failed": "Failed", "none": "Unprocessed", "parseFailed": "Parse Failed", "parseSuccess": "Parse Success", "parsing": "Parsing", "parsingProgress": "Parsing ({percent})", "queueing": "Queueing", "success": "Success", "unknown": "Unknown Status", "unparsed": "Unparsed"}, "title": "Knowledge Base Management", "unknownError": "Unknown error"}, "login": {"description": "Please enter your username and password to log in", "password": "password", "passwordPlaceholder": "Enter your password", "submitting": "Login...", "title": "Log in", "username": "Username", "usernameMin": "The username requires at least 2 characters"}, "model": {"cancel": "Cancel", "chatModel": "Chat Model", "confirm": "Confirm", "description": "You must select the required models before use", "embeddingModel": "Embedding Model", "nl2pythonModel": "NL2PYTHON Model", "nl2sqlModel": "NL2SQL Model", "placeholder": "Please select an appropriate model", "rerankModel": "<PERSON><PERSON>", "save": "Save", "saveFailed": "Failed to save model configuration", "saveSuccess": "Model configuration saved successfully", "saving": "Saving...", "selectChatModel": "Select Chat Model", "selectEmbeddingModel": "Select Embedding Model", "submitting": "Submitting...", "title": "Set Default Models", "validation": {"selectChatModel": "Please select an appropriate chat model", "selectEmbeddingModel": "Please select an appropriate embedding model", "selectModel": "Please select an appropriate model"}}, "password": {"cancel": "Cancel", "changeDescription": "Please enter a new password to complete the change", "changeFailed": "Failed to change password, please try again", "changeSuccess": "Password changed successfully! Dialog will close automatically...", "changeTitle": "Change Account Password", "confirm": "Confirm Change", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter new password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "submitting": "Submitting...", "validation": {"confirmPasswordRequired": "Confirm password requires at least 6 characters", "newPasswordRequired": "New password requires at least 6 characters", "passwordNotMatch": "New password and confirm password do not match"}}, "session": {"loadingSessionInfo": "Loading session information...", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "title": "Session"}, "sidebar": {"agent": {"addAgent": "Add Agent", "agentList": "Agent List", "createSuccess": "Agent created successfully", "myAgent": "My Agent", "teamAgent": "Team Agent"}, "projects": {"datasets": "Datasets", "files": "Files", "knowledgeBase": "Knowledge Base", "modelConfig": "Model Configuration", "teamManagement": "Team Management"}, "team": {"admin": "Admin", "enterpriseSpace": "Enterprise Space", "member": "Member", "personalSpace": "Personal Space", "personalSpaceSuffix": "'s Personal Space", "spaceSuffix": "'s Space"}, "user": {"changePassword": "Change Password", "defaultUser": "User", "logout": "Log out", "personalSpace": "Personal Space", "personalSpaceSuffix": "'s Personal Space", "spaceSuffix": "'s Space"}}, "sliceMethod": {"naive": {"description": "Default slice size is 512, users can set each file individually"}, "supportedFormats": "This slice method supports the following file formats:"}, "team": {"accountName": "Account Name", "actions": "Actions", "active": "Active", "addMember": "Add Member", "addMembers": {"addButton": "Add Members", "description": "Select members to add to the team from existing accounts", "emptyText": "No matching members found", "endText": "Reached end of list", "loadingText": "Loading...", "searchPlaceholder": "Search members...", "selectedCount": "Selected {count} members", "title": "Add Team Members", "unknownUser": "Unknown User"}, "admin": "Admin", "delete": "Delete", "deleteMember": {"description": "Are you sure you want to delete member \"{memberName}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteMemberTip": "Delete Member", "email": "Email", "inactive": "Inactive", "joinTime": "Join Time", "lastActive": "Last Active", "makeAdmin": "Make Admin", "member": "Member", "messages": {"addFailed": "Failed to add member", "addSuccess": "Member added successfully", "deleteFailed": "Failed to delete member", "deleteSuccess": "Member deleted successfully", "fetchFailed": "Failed to fetch available members", "removeFailed": "Failed to remove member", "removeSuccess": "Member removed successfully"}, "name": "Name", "pending": "Pending", "removeAdmin": "Remove <PERSON>", "removeMember": "Remove Member", "role": "Role", "searchPlaceholder": "Search members...", "selectedCount": "Selected {count} members", "status": "Status", "title": "Team Members", "unknownUser": "Unknown User"}, "test": {"button": {"cancel": "Cancel", "save": "Save"}, "description": "This is a component for testing i18n-ally plugin functionality.", "instructions": "Please click on translation keys in VSCode to check if you can correctly navigate to translation files and sync edits.", "note": "If you can see this English text, the translation system is working properly. Now please try editing these translation keys.", "title": "i18n-ally Test Component"}}, "management": {"common": {"actions": "Actions", "active": "Active", "add": "Add", "and": "and", "apply": "Apply", "back": "Back", "cancel": "Cancel", "clear": "Clear", "close": "Close", "confirm": "Confirm", "confirmPassword": "Confirm New Password", "create": "Create", "delete": "Delete", "description": "Description", "deselect": "Deselect", "deselectAll": "Deselect All", "details": "Details", "disabled": "Disabled", "download": "Download", "edit": "Edit", "empty": "Empty", "enabled": "Enabled", "error": "Error", "export": "Export", "failed": "Failed", "false": "No", "filter": "Filter", "forgotPassword": "Forgot Password", "hidden": "Hidden", "import": "Import", "inactive": "Inactive", "invalid": "Invalid", "loading": "Loading...", "login": "<PERSON><PERSON>", "loginWith": "Login with {provider}", "logout": "Logout", "name": "Name", "newPassword": "New Password", "next": "Next", "no": "No", "noData": "No data", "noResults": "No results", "off": "Off", "offline": "Offline", "ok": "OK", "on": "On", "online": "Online", "optional": "Optional", "or": "or", "password": "Password", "previous": "Previous", "private": "Private", "profile": "Profile", "public": "Public", "refresh": "Refresh", "register": "Register", "rememberMe": "Remember Me", "required": "Required", "reset": "Reset", "retry": "Retry", "save": "Save", "search": "Search", "select": "Select", "selectAll": "Select All", "settings": "Settings", "sort": "Sort", "status": "Status", "submit": "Submit", "submitting": "Submitting...", "success": "Success", "time": "Time", "true": "Yes", "update": "Update", "upload": "Upload", "username": "Username", "valid": "<PERSON><PERSON>", "view": "View", "visible": "Visible", "yes": "Yes"}, "form": {"emptyAdmin": "Please select at least one team admin", "emptyLoginName": "Admin login username cannot be empty", "emptyLoginPassword": "Admin login password cannot be empty", "emptyModel": "Please select a valid model", "emptySiteName": "Site name cannot be empty", "emptyTeamName": "Team name cannot be empty", "enterTeamName": "Please enter team name", "inputTeamName": "Enter team name", "required": "Required", "selectAdminRequired": "Please select at least one team admin"}, "login": {"description": "Please enter your username and password to log in", "loggingIn": "Logging in...", "login": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed, please try again", "passwordLabel": "Password", "passwordPlaceholder": "Enter password", "title": "<PERSON><PERSON>", "usernameLabel": "Username", "usernameMinLength": "Username must be at least 2 characters", "usernamePlaceholder": "Enter username", "verifying": "Verifying identity..."}, "misc": {"addUser": "Add User", "confirmDelete": "Confirm Delete", "delete": "Delete", "edit": "Edit", "unassigned": "Unassigned", "view": "View"}, "models": {"addLLM": "Add LLM", "apiKeyPlaceholder": "Enter API Key", "apiKeyRequired": "API Key cannot be empty", "apiVersionRequired": "API version cannot be empty", "baseUrlPlaceholder": "Enter Base URL", "baseUrlRequired": "Base URL cannot be empty", "config": "Model Config", "configFail": "Model configuration failed, please check your input and try again", "configSuccess": "Model configured successfully", "configuredModels": "Configured Models", "confirmDelete": "Are you sure you want to delete the model \"{model}\"? This action cannot be undone.", "deleteFail": "Failed to delete model", "deleteModel": "Delete Model", "deleteSuccess": "Model deleted successfully", "editConfig": "Edit Config", "inputModelAlias": "Enter model alias", "inputModelName": "Enter model name", "inputModelPurpose": "Enter model purpose", "inputModelType": "Enter model type", "maxTokensRequired": "Please select a valid max token count", "modelAliasRequired": "Model alias cannot be empty", "modelNameRequired": "Model name cannot be empty", "modelPurposeRequired": "Model purpose cannot be empty", "modelSettings": "Model Settings", "modelTypeRequired": "Model type cannot be empty", "selectModelPurpose": "Select model purpose", "selectModelType": "Select model type", "unconfiguredModels": "Unconfigured Models"}, "nav": {"dashboard": "Dashboard", "models": "Models", "teams": "Teams", "users": "Users"}, "password": {"changeDescription": "Please enter your current and new password to proceed", "changeFail": "Failed to change password", "changeSuccess": "Password changed successfully", "changeTitle": "Change Super Admin Password", "confirmChange": "Confirm Change", "confirmMinLength": "Confirmation password must be at least 6 characters", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Re-enter new password", "minLength": "New password must be at least 6 characters", "mismatch": "New password and confirmation do not match", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password"}, "sidebar": {"changePassword": "Change Password", "logout": "Logout", "model": "Model Config", "siteConfig": "Site Configuration", "team": "Team Management", "user": "Account Management"}, "site": {"configUpdateFail": "Failed to update site configuration", "configUpdateSuccess": "Site configuration updated successfully", "description": "{name} is an AI service centered on personal and enterprise datasets, designed to unlock the full potential of your data.", "fileUploadFail": "File upload failed", "fileUploadSuccess": "File uploaded successfully", "initLoginName": "Please initialize admin login username", "initLoginPassword": "Please initialize admin login password", "inputSiteName": "Please enter site name", "title": "{name} - Get insights quickly from knowledge and data"}, "siteConfig": {"adminLoginPassword": "Admin Login Password", "adminLoginUsername": "<PERSON><PERSON>", "cancel": "Cancel", "darkLogo": "Dark Logo", "darkLogoDescription": "Recommended size: 140px * 40px, this icon is displayed on the login page in dark mode", "description": "Configure site name, logo, and icons", "favicon": "Favicon", "faviconDescription": "Recommended size: 16px * 16px, this icon is displayed in the browser tab", "logo": "Logo", "logoDescription": "Recommended size: 140px * 40px, this icon is displayed on the login page", "messages": {"updateFailed": "Failed to update site configuration", "updateSuccess": "Site configuration updated successfully", "uploadFailed": "File upload failed", "uploadSuccess": "File uploaded successfully"}, "miniDarkLogo": "Mini Dark Logo", "miniDarkLogoDescription": "Recommended size: 40px * 40px, this icon is displayed in the navigation bar in dark mode", "miniLogo": "Mini Logo", "miniLogoDescription": "Recommended size: 40px * 40px, this icon is displayed in the navigation bar", "placeholders": {"adminLoginPassword": "Please initialize admin login password", "adminLoginUsername": "Please enter admin login username", "siteName": "Please enter site name"}, "save": "Save", "siteConfig": "Site configuration", "siteName": "Site Name", "submitting": "Submitting...", "upload": "Upload", "uploadImage": "Upload Image", "validation": {"adminLoginPasswordRequired": "Admin login password cannot be empty", "adminLoginUsernameRequired": "Admin login username cannot be empty", "siteNameRequired": "Site name cannot be empty"}}, "teams": {"admin": "Admin", "create": {"fail": {"tips": "Failed to create team"}}, "createTeam": "Create Team", "createdAt": "Created At", "delete": {"confirm": "Are you sure you want to delete the team \"{name}\"? This action cannot be undone.", "fail": {"title": "Failed to delete team"}}, "details": "Team Details", "editTeam": "Edit Team", "form": {"adminDescription": "Team admins have permission to manage team members", "adminLabel": "Team Admin", "adminRequired": "Please select at least one team admin", "allUsersLoaded": "All users loaded", "chatModelLabel": "Chat Model", "chatModelPlaceholder": "Please select a valid chat model", "chatModelRequired": "Please select a valid model", "createButton": "Create Team", "embdModelLabel": "Embedding Model", "embdModelPlaceholder": "Please select a valid embedding model", "embdModelRequired": "Please select a valid model", "loading": "Loading...", "loadingMore": "Loading more...", "loadingUsers": "Failed to load users", "nl2pythonModelLabel": "NL2Python Model", "nl2pythonModelPlaceholder": "Please select a valid NL2Python model", "nl2pythonModelRequired": "Please select a valid model", "nl2sqlModelLabel": "NL2SQL Model", "nl2sqlModelPlaceholder": "Please select a valid NL2SQL model", "nl2sqlModelRequired": "Please select a valid model", "noUsersFound": "No users found", "rerankModelLabel": "<PERSON><PERSON>", "rerankModelPlaceholder": "Please select a valid rerank model", "rerankModelRequired": "Please select a valid model", "saveButton": "Save Changes", "saveTeamFailed": "Failed to save team", "scrollToLoadMore": "Scroll down to load more", "searchUserPlaceholder": "Search users...", "searching": "Searching...", "selectAdminPlaceholder": "Select team admin", "selectedAdminCount": "{count} admin(s) selected", "teamNameLabel": "Team Name", "teamNamePlaceholder": "Enter team name", "teamNameRequired": "Team name cannot be empty", "unknownUser": "User-{id}"}, "member": "Member", "memberCount": "Members", "memberName": "Member Name", "name": "Team Name", "noTeamMembers": "No team members yet", "role": "Role", "searchUser": "Search users...", "selectAdmin": "Select Team Admin", "selectChatModel": "Please select a valid chat model", "selectEmbedModel": "Please select a valid embed model", "selectNL2PythonModel": "Please select a valid NL2Python model", "selectNL2SQLModel": "Please select a valid NL2SQL model", "selectRerankModel": "Please select a valid rerank model", "selectedAdminCount": "{count} admin(s) selected", "teamMembers": "Team Members", "unassigned": "Unassigned", "update": {"fail": {"title": "Failed to update team"}}}, "users": {"accountName": "Account Name", "accountNameRequired": "Account name cannot be empty", "actions": "Actions", "create": {"fail": {"title": "Failed to create user"}}, "createAccount": "Create Account", "createUser": "Create Account", "createdAt": "Created At", "delete": {"confirm": "Are you sure you want to delete the account \"{login_name}\"? This action cannot be undone.", "fail": {"title": "Failed to delete user"}}, "editUser": "Edit Account", "enterAccountName": "Please enter account name", "inputAccountName": "Enter account name", "inputPassword": "Enter password", "leaveEmptyForNoChange": "Leave empty to keep unchanged", "loginName": "Account Name", "manage": "Account Management", "modifyPassword": "Modify Password", "noChangePasswordHint": "If you don't need to change the password, please leave it empty", "passwordRequired": "Password must be at least 6 characters", "saveChanges": "Save Changes", "update": {"fail": {"title": "Failed to update user"}}}}}