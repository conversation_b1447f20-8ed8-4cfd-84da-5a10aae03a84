"use client";

import { useCreateFolder } from "@/service";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { AddFileDropdown } from "./components/add-file-dropdown";
import { BreadcrumbNav } from "./components/breadcrumb-nav";
import { CreateFolderDialog } from "./components/create-folder-dialog";
import { FileList } from "./components/file-list";
import { FileUpload } from "./components/file-upload";

/**
 * 文件管理页面
 */
export default function FilesPageComponent() {
  const { toast } = useToast();
  const t = useTranslations("web.files");
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isCreateFolderDialogOpen, setIsCreateFolderDialogOpen] = useState(false);
  // 文件夹导航状态
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [currentPath, setCurrentPath] = useState("/");
  const [folderPath, setFolderPath] = useState<Array<{ id: string; name: string }>>([]);
  const [uploadSuccessTrigger, setUploadSuccessTrigger] = useState(0);

  const createFolder = useCreateFolder();

  // 处理文件夹点击
  const handleFolderClick = (folderId: string, folderName: string) => {
    setCurrentFolderId(folderId);
    const newPath = currentPath === "/" ? `/${folderName}` : `${currentPath}/${folderName}`;

    setCurrentPath(newPath);
    setFolderPath([...folderPath, { id: folderId, name: folderName }]);
  };

  // 处理面包屑导航
  const handleBreadcrumbNavigate = (path: string) => {
    if (path === "/") {
      // 返回根目录
      setCurrentFolderId(null);
      setCurrentPath("/");
      setFolderPath([]);
    } else {
      // 导航到指定路径
      const pathSegments = path.split("/").filter(Boolean);
      const newFolderPath: Array<{ id: string; name: string }> = [];
      let newCurrentFolderId: string | null = null;

      // 根据路径找到对应的文件夹ID
      for (let i = 0; i < pathSegments.length && i < folderPath.length; i++) {
        const folderItem = folderPath[i];

        if (folderItem && folderItem.name === pathSegments[i]) {
          newFolderPath.push(folderItem);
          newCurrentFolderId = folderItem.id;
        } else {
          break;
        }
      }

      setCurrentFolderId(newCurrentFolderId);
      setCurrentPath(path);
      setFolderPath(newFolderPath);
    }
  };

  // 处理创建文件夹
  const handleCreateFolder = (folderName: string) => {
    createFolder.mutate(
      {
        name: folderName,
        parentId: currentFolderId,
      },
      {
        onSuccess: (newFolder) => {
          toast({
            title: t("messages.createFolderSuccess"),
            description: t("messages.createFolderSuccessDesc", { folderName }),
          });
          // 如果当前在文件夹内，创建成功后进入该文件夹
          if (currentFolderId) {
            handleFolderClick(newFolder.id, newFolder.name);
          }
          // 创建文件夹成功后，重置分页状态到第一页
          setUploadSuccessTrigger((prev) => prev + 1);
        },
        onError: (error) => {
          toast({
            title: t("messages.createFolderFailed"),
            description: t("messages.createFolderFailedDesc"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理文件上传成功
  const handleUploadSuccess = () => {
    // 文件上传成功后，重置分页状态到第一页
    // 通过设置一个状态来触发 FileList 组件的分页重置
    setUploadSuccessTrigger((prev) => prev + 1);
  };

  return (
    <CustomContainer
      title={t("title")}
      action={
        <AddFileDropdown
          onUploadFile={() => setIsUploadDialogOpen(true)}
          onCreateFolder={() => setIsCreateFolderDialogOpen(true)}
          isInFolder={!!currentFolderId}
        />
      }
    >
      {/* 面包屑导航 */}
      <BreadcrumbNav currentPath={currentPath} onNavigate={handleBreadcrumbNavigate} />

      <FileList
        onFolderClick={handleFolderClick}
        currentFolderId={currentFolderId}
        onUploadSuccess={handleUploadSuccess}
        uploadSuccessTrigger={uploadSuccessTrigger}
      />

      {/* 上传文件对话框 */}
      {isUploadDialogOpen && (
        <FileUpload
          isUploadDialogOpen={isUploadDialogOpen}
          setIsUploadDialogOpen={setIsUploadDialogOpen}
          parentId={currentFolderId}
          onUploadSuccess={handleUploadSuccess}
        />
      )}

      {/* 创建文件夹对话框 */}
      <CreateFolderDialog
        open={isCreateFolderDialogOpen}
        onClose={() => setIsCreateFolderDialogOpen(false)}
        onCreateFolder={handleCreateFolder}
        currentPath={currentPath}
      />
    </CustomContainer>
  );
}
