"use client";

import { useSiteConfig } from "@/service/site-config-service";
import { siteConfigAtom } from "@/store/user-store";
import { useAtomValue } from "jotai";
import { useTranslations } from "next-intl";
import Head from "next/head";
import { useEffect } from "react";

export function SiteConfigProvider({ children }: { children: React.ReactNode }) {
  const t = useTranslations("management");
  const siteConfig = useAtomValue(siteConfigAtom);

  useSiteConfig();
  const baseUrl = process.env.MANAGEMENT_API_URL || "";

  // 处理查询结果，更新 atom 状态（避免重复设置相同数据）

  useEffect(() => {
    // 只有当 siteConfig 存在时才执行更新逻辑
    if (!siteConfig) {
      return;
    }

    // Update favicon when site config changes
    if (siteConfig.favicon) {
      const favicon = document.querySelector("link[rel='icon']");

      if (favicon) {
        favicon.setAttribute("href", `${baseUrl}/_${siteConfig.favicon}`);
      } else {
        const link = document.createElement("link");

        link.rel = "icon";
        link.href = `${baseUrl}/_${siteConfig.favicon}`;
        document.head.appendChild(link);
      }
    }

    // Update document title when site config changes
    if (siteConfig.name) {
      document.title = t("site.title", { name: siteConfig.name });
      document
        .querySelector("meta[name='description']")
        ?.setAttribute("content", t("site.description", { name: siteConfig.name }));
    }
  }, [siteConfig, baseUrl, t]);

  return (
    <>
      <Head>
        {siteConfig?.favicon && <link rel="icon" href={`${baseUrl}/_${siteConfig.favicon}`} />}
      </Head>
      {children}
    </>
  );
}
