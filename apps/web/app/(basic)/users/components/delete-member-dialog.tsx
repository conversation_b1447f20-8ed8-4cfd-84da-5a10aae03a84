"use client";

import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { useTranslations } from "next-intl";

interface DeleteMemberDialogProps {
  open: boolean;
  onClose: () => void;
  onDelete: () => void;
  memberName: string;
}

export function DeleteMemberDialog({
  open,
  onClose,
  onDelete,
  memberName,
}: DeleteMemberDialogProps) {
  const t = useTranslations("web.team.deleteMember");

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onClose}
      onConfirm={onDelete}
      title={t("title")}
      description={t("description", { memberName })}
    />
  );
}
