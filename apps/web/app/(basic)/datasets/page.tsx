"use client";
import { useConfig } from "@/components/config-provider-client";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useTranslations } from "next-intl";
import { DatasetsTabs } from "./components/datasetsTabs";

export default function DatasetsPage() {
  const { SHOW_NL2CODE } = useConfig();
  const t = useTranslations("web.datasets");

  let tabs: any = [];

  if (SHOW_NL2CODE === "true") {
    tabs = [
      { label: t("fileDataset"), value: "filesets" },
      { label: t("databaseDataset"), value: "dbsets" },
    ];
  } else {
    tabs = [
      { label: t("databaseDataset"), value: "dbsets" },
      { label: t("fileDataset"), value: "filesets" },
    ];
  }

  return (
    <>
      <CustomContainer title={t("title")}>
        <DatasetsTabs tabs={tabs} />
      </CustomContainer>
    </>
  );
}
