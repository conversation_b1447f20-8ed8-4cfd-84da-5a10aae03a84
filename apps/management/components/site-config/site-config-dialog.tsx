"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { useTranslations } from "next-intl";
import { SiteConfigForm } from "./site-config-form";

interface SiteConfigDialogProps {
  open: boolean;
  onClose: () => void;
}

export function SiteConfigDialog({ open, onClose }: SiteConfigDialogProps) {
  const t = useTranslations("management.siteConfig");

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="flex flex-col">
        <DialogHeader className="flex-none">
          <DialogTitle>{t("siteConfig")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <SiteConfigForm onClose={onClose} />
      </DialogContent>
    </Dialog>
  );
}
