"use client";

import { checkIsOwner } from "@/lib/user-role";
import { formatDateTime } from "@/lib/utils";
import { type Dataset } from "@/service/dataset-service";
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { BookText, Pencil, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface DatasetCardProps {
  dataset: Dataset;
  onClick: () => void;
  onDelete: () => void;
  onEdit?: (dataset: Dataset, event: React.MouseEvent) => void;
}

export function FileCard({ dataset, onClick, onDelete, onEdit }: DatasetCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const isOwner = checkIsOwner(dataset.creator?.id, dataset.creator?.admin);
  const t = useTranslations("web.datasets.card");
  const tDelete = useTranslations("web.datasets.delete");

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await onDelete();
    setIsDeleteDialogOpen(false);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(dataset, e);
    }
  };

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  };

  return (
    <>
      <Card
        className="relative cursor-pointer transition-colors"
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CardHeader className="flex w-full items-center justify-between gap-2">
          <CardTitle className="flex w-full min-w-0 flex-1 items-center gap-2 text-base font-medium">
            <BookText className="text-primary h-4 w-4 flex-shrink-0" />
            <span className="block w-full truncate">{dataset.name}</span>
          </CardTitle>
          {/* 按钮组 - 仅在悬停时显示 */}
          {isHovered && (
            <div className="flex flex-shrink-0 gap-1">
              <IconButton
                variant="ghost"
                tooltip={t("delete")}
                onClick={handleOpenDeleteDialog}
                disabled={!isOwner}
              >
                <Trash2 className="text-destructive h-3 w-3" />
              </IconButton>
              <IconButton variant="ghost" tooltip={t("edit")} onClick={handleEdit}>
                <Pencil className="h-3 w-3" />
              </IconButton>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground space-y-1 text-sm">
            <p>
              <span className="font-medium">{t("description")}</span>{" "}
              {dataset.description || t("none")}
            </p>
            <p>
              <span className="font-medium">{t("createTime")}</span>{" "}
              {dataset.create_time ? formatDateTime(new Date(dataset.create_time)) : t("none")}
            </p>
            <p>
              <span className="font-medium">{t("creator")}</span> {dataset.creator?.name}
            </p>
          </div>
        </CardContent>
        {/* 删除确认对话框 */}
      </Card>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDelete}
        title={tDelete("title")}
        description={tDelete("description", { name: dataset?.name || "" })}
      />
    </>
  );
}
