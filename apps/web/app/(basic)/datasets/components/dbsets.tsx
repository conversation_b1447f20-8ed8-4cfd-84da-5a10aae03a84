"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Card, CardContent } from "@ragtop-web/ui/components/card";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { ChevronLeft, ChevronRight, Loader2, PlusIcon, RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { DatasetCard } from "./dataset-card";
import { DatasetForm } from "./dataset-form";

import {
  useBuildDataset,
  useCreateDataset,
  useDatasets,
  useDeleteDataset,
  useUpdateDataset,
  type CreateDatasetParams,
  type Dataset,
  type UpdateDatasetParams,
} from "@/service/dataset-service";
export default function Dbsets() {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("web.datasets");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // 使用dataset-service获取数据集列表
  const {
    data: datasetsResponse,
    isLoading,
    error,
    refetch,
  } = useDatasets(currentPage, pageSize, true);

  // 从响应中提取数据集列表
  const datasets = datasetsResponse?.records || [];
  const total = datasetsResponse?.total || 0;
  const totalPages = Math.ceil(total / pageSize);

  // 数据集操作hooks
  const createDataset = useCreateDataset();
  const updateDataset = useUpdateDataset();
  const buildDataset = useBuildDataset();
  const deleteDataset = useDeleteDataset();

  // 处理打开数据集详情页面
  const handleOpenDataset = (dataset: Dataset) => {
    router.push(`/datasets/database/${dataset.id}`);
  };

  // 处理打开创建数据集抽屉
  const handleCreateDataset = () => {
    setSelectedDataset(null);
    setIsCreating(true);
    setIsDrawerOpen(true);
  };

  // 处理编辑数据集
  const handleEditDataset = (dataset: Dataset, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedDataset(dataset);
    setIsCreating(false);
    setIsDrawerOpen(true);
  };

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  // 处理保存数据集
  const handleSaveDataset = async (datasetData: CreateDatasetParams | UpdateDatasetParams) => {
    if (isCreating) {
      // 创建新数据集
      await createDataset.mutateAsync(datasetData as CreateDatasetParams, {
        onSuccess: () => {
          toast({
            title: t("messages.createSuccess"),
            description: t("messages.datasetCreated"),
          });
        },
        onError: () => {
          toast({
            title: t("messages.createFailed"),
            description: t("messages.createError"),
            variant: "destructive",
          });
        },
      });
    } else if (selectedDataset) {
      // 更新现有数据集
      await updateDataset.mutateAsync(
        {
          ...datasetData,
          tableset_id: selectedDataset.id,
        } as UpdateDatasetParams,
        {
          onSuccess: () => {
            toast({
              title: t("messages.updateSuccess"),
              description: t("messages.datasetUpdated"),
            });
            // 更新成功后刷新列表，useDatasets会自动通过queryClient.invalidateQueries刷新
          },
          onError: (e) => {
            console.log("e", e);
            toast({
              title: t("messages.updateFailed"),
              description: t("messages.updateError"),
              variant: "destructive",
            });
          },
        }
      );
    }
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 处理构建数据集
  const handleBuildDataset = (dataset: Dataset, e: React.MouseEvent) => {
    e.stopPropagation();

    buildDataset.mutate(
      { tableset_id: dataset.id || "" },
      {
        onSuccess: () => {
          toast({
            title: t("messages.buildSuccess"),
            description: t("messages.buildStarted"),
          });
        },
        onError: () => {
          toast({
            title: t("messages.buildFailed"),
            description: t("messages.buildError"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理删除知识库
  const handleDeleteDataset = (datasetId: string) => {
    deleteDataset.mutate(
      {
        tableset_id: datasetId,
      },
      {
        onSuccess: () => {
          toast({
            title: t("messages.deleteSuccess"),
            description: t("messages.databaseDatasetDeleted"),
          });
        },
        onError: () => {
          toast({
            title: t("messages.deleteFailed"),
            description: t("messages.deleteDatabaseError"),
            variant: "destructive",
          });
        },
      }
    );
  };

  return (
    <CustomContainer title="">
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" strokeWidth={1.5} />
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="py-12 text-center">
          <div className="text-destructive mb-4">
            加载数据集列表失败: {error.message || "未知错误"}
          </div>
          <Button variant="outline" onClick={() => refetch()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            重试
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* 添加数据集卡片 */}
            <Card
              className="hover:border-primary/50 cursor-pointer items-center justify-center border-dashed transition-colors"
              onClick={handleCreateDataset}
            >
              <CardContent className="flex items-center justify-center">
                <div className="text-muted-foreground flex flex-col items-center">
                  <PlusIcon className="mb-2 h-10 w-10" />
                  <span className="text-md">{t("addDatabaseDataset")}</span>
                </div>
              </CardContent>
            </Card>

            {/* 数据集卡片列表 */}
            {datasets.map((dataset) => (
              <DatasetCard
                key={dataset.id}
                dataset={dataset}
                onDelete={() => handleDeleteDataset(dataset.id)}
                onClick={() => handleOpenDataset(dataset)}
                onBuild={handleBuildDataset}
                onEdit={handleEditDataset}
              />
            ))}
          </div>

          {/* 分页控制 */}
          {totalPages > 1 && (
            <div className="mt-8 flex items-center justify-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>

              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-sm">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <span className="text-muted-foreground text-sm">(共 {total} 条记录)</span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="flex items-center gap-2"
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {!isLoading && datasets.length === 0 && (
            <div className="text-muted-foreground py-12 text-center">{t("emptyMessage")}</div>
          )}
        </>
      )}

      {/* 数据集抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={isCreating ? t("form.create") : t("form.update")}
      >
        <DatasetForm
          dataset={isCreating ? undefined : selectedDataset!}
          onSave={async (formData) => {
            const apiData = formData as UpdateDatasetParams;

            await handleSaveDataset(apiData);
            setIsDrawerOpen(false);
          }}
          isLoading={createDataset.isPending || updateDataset.isPending}
          isCreating={isCreating}
        />
      </CustomDrawer>
    </CustomContainer>
  );
}
