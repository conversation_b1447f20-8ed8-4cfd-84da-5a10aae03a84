# i18n-ally 使用指南

## 概述

本项目使用 [i18n-ally](https://github.com/lokalise/i18n-ally)
VSCode 扩展来提供强大的国际化开发体验。

## 安装

1. 在 VSCode 中安装 `i18n-ally` 扩展
2. 重启 VSCode

## 功能特性

### 1. 翻译键预览

- 在代码中直接显示翻译内容
- 支持嵌套键的预览
- 多语言内容对比

### 2. 快速跳转

- 点击翻译键直接跳转到翻译文件
- 支持从翻译文件跳转回使用位置

### 3. 自动补全

- 输入翻译键时提供智能补全
- 显示可用的翻译键列表

### 4. 翻译管理

- 可视化翻译编辑器
- 批量翻译操作
- 翻译进度跟踪

## 项目配置

### 翻译文件位置

```
packages/i18n/dictionaries/
├── en.json  # 英文翻译
└── zh.json  # 中文翻译
```

### 配置文件

- `.vscode/settings.json` - VSCode 工作区设置
- `.vscode/i18n-ally.config.json` - i18n-ally 专用配置
- `project.inlang/settings.json` - Inlang 项目配置

## 使用方法

### 1. 查看翻译预览

在包含 `useTranslations` 的组件中，翻译键会显示对应的翻译内容：

```tsx
const t = useTranslations("web.test");
// 鼠标悬停在 "title" 上会显示翻译内容
return <h1>{t("title")}</h1>;
```

### 2. 跳转到翻译文件

- 按住 `Ctrl/Cmd` 点击翻译键
- 或右键选择 "Go to Definition"

### 3. 添加新翻译

1. 在代码中使用新的翻译键
2. i18n-ally 会提示缺失的翻译
3. 点击提示快速添加翻译

### 4. 编辑翻译

1. 在翻译文件中直接编辑
2. 或使用 i18n-ally 的可视化编辑器

## 测试功能

### 运行测试脚本

```bash
# 检查 i18n 配置
pnpm i18n:test

# 重置 i18n-ally 缓存
pnpm i18n:reset
```

### 访问测试页面

访问 `/test-i18n` 页面查看翻译效果并测试功能。

## 故障排除

### 1. 翻译预览不显示

- 检查是否安装了 i18n-ally 扩展
- 重启 VSCode
- 运行 `pnpm i18n:reset` 清理缓存

### 2. 无法跳转到翻译文件

- 检查翻译文件路径是否正确
- 确认翻译键存在于翻译文件中
- 检查 VSCode 设置中的路径配置

### 3. 自动补全不工作

- 确保在 `useTranslations` 的作用域内
- 检查 TypeScript 配置
- 重启 TypeScript 语言服务

## 最佳实践

1. **使用嵌套键结构**

   ```json
   {
     "web": {
       "common": {
         "save": "Save",
         "cancel": "Cancel"
       }
     }
   }
   ```

2. **保持键名一致性**

   - 使用 kebab-case 命名
   - 按功能模块组织
   - 避免过深的嵌套

3. **及时同步翻译**
   - 添加新功能时同时添加翻译
   - 定期检查缺失的翻译
   - 保持所有语言的翻译完整性

## 相关链接

- [i18n-ally 官方文档](https://github.com/lokalise/i18n-ally)
- [next-intl 文档](https://next-intl-docs.vercel.app/)
- [项目 i18n 配置](../packages/i18n/)
