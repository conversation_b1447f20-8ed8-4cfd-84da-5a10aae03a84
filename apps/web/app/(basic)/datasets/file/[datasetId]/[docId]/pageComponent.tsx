"use client";

import {
  Sheet,
  useDataset,
  useFilesetDocRequestMetadata,
  useFilesetDocSaveAndParse,
} from "@/service/dataset-service";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb";
import { Button } from "@ragtop-web/ui/components/button";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { FileText, Play, Table } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import WorksheetContent from "./components/worksheet-content";
import WorksheetList from "./components/worksheet-list";

// 定义数据类型
interface NavigationState {
  worksheetIndex?: number;
  subtableIndex?: number;
}

export default function FileDetails({ datasetId, docId }: { datasetId: string; docId: string }) {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("web.datasets.fileDetail");
  const [isInitialized, setIsInitialized] = useState(false);
  const [navigationState, setNavigationState] = useState<NavigationState>({});
  const [hasChanges, setHasChanges] = useState(false);
  // 本地状态管理工作表数据
  const [localWorksheets, setLocalWorksheets] = useState<Sheet[]>([]);
  // 面包屑状态
  const [breadcrumbs, setBreadcrumbs] = useState<
    Array<{ name: string; type: "worksheets" | "worksheet" | "subtable" }>
  >([{ name: "File", type: "worksheets" }]);

  // API hooks
  const { data: dataset, isError } = useDataset(datasetId, isInitialized);
  const saveAndParseMutation = useFilesetDocSaveAndParse();
  const {
    data: metadata,
    isLoading: isMetadataLoading,
    isError: isMetadataError,
  } = useFilesetDocRequestMetadata({ tableset_id: datasetId, doc_id: docId });

  // 当 metadata 更新时，同步到本地状态
  useEffect(() => {
    if (metadata && metadata.length > 0) {
      setLocalWorksheets(metadata);
    }
  }, [metadata]);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  // 处理工作表选择
  const handleWorksheetSelect = useCallback(
    (worksheetIndex: number) => {
      const worksheet = localWorksheets[worksheetIndex];

      if (!worksheet) {
        return;
      }
      const worksheetName =
        worksheet.name || worksheet.sheet_name || `${t("worksheet")} ${worksheetIndex + 1}`;

      setNavigationState({ worksheetIndex });
      setBreadcrumbs([
        { name: t("file"), type: "worksheets" },
        { name: `${worksheetName}(${t("worksheet")})`, type: "worksheet" },
      ]);
    },
    [localWorksheets, t]
  );

  // 处理子表选择
  const handleSubtableSelect = useCallback(
    (worksheetIndex: number, subtableIndex: number) => {
      const worksheet = localWorksheets[worksheetIndex];

      if (!worksheet) {
        return;
      }
      const worksheetName =
        worksheet.name || worksheet.sheet_name || `${t("worksheet")} ${worksheetIndex + 1}`;
      const subtables = worksheet.subtables ?? [];
      const subtable = subtables[subtableIndex];
      const subtableName = subtable?.title || `${t("subtable")} ${subtableIndex + 1}`;

      setNavigationState({ worksheetIndex, subtableIndex });
      setBreadcrumbs([
        { name: t("file"), type: "worksheets" },
        { name: `${worksheetName}(${t("worksheet")})`, type: "worksheet" },
        { name: subtableName, type: "subtable" },
      ]);
    },
    [localWorksheets, t]
  );

  // 处理工作表数据更新
  const handleWorksheetUpdate = useCallback((updatedWorksheets: Sheet[]) => {
    setLocalWorksheets(updatedWorksheets);
    setHasChanges(true);
  }, []);

  // 处理构建数据集
  const handleBuildDataset = () => {
    if (!dataset?.id || localWorksheets.length === 0) {
      return;
    }
    // Convert Sheet[] (null -> undefined, recursively)
    const sheets: Sheet[] = localWorksheets.map((ws) => ({
      ...ws,
      semantic_name: ws.semantic_name ?? undefined,
      semantic_comment: ws.semantic_comment ?? undefined,
      subtables: (ws.subtables ?? []).map((st) => ({
        ...st,
        title: st.title ?? undefined,
        footnote: st.footnote ?? undefined,
        footnote_range: st.footnote_range ?? undefined,
        columns: (st.columns ?? []).map((col) => ({
          ...col,
          semantic_name: col.semantic_name ?? undefined,
          semantic_comment: col.semantic_comment ?? undefined,
        })),
      })),
    }));

    saveAndParseMutation.mutate(
      {
        tableset_id: dataset.id,
        doc_id: docId,
        worksheets: sheets,
      },
      {
        onSuccess: () => {
          toast({
            title: t("saveSuccess"),
            description: t("saveSuccessDesc"),
          });
          setHasChanges(false);
          router.push(`/datasets/file/${datasetId}`);
        },
        onError: () => {
          toast({
            title: t("saveFailed"),
            description: t("saveFailedDesc"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // Convert Sheet[] to WorksheetData[] for WorksheetList/WorksheetContent props
  const worksheetDataList = localWorksheets.map((ws) => ({
    ...ws,
    semantic_name: ws.semantic_name ?? null,
    semantic_comment: ws.semantic_comment ?? null,
    subtables: (ws.subtables ?? []).map((st) => ({
      ...st,
      title: st.title ?? null,
      footnote: st.footnote ?? null,
      footnote_range: st.footnote_range ?? null,
      range: st.range ?? "",
      title_range: st.title_range ?? "",
      data_range: st.data_range ?? "",
      columns: (st.columns ?? []).map((col) => ({
        ...col,
        semantic_name: col.semantic_name ?? null,
        semantic_comment: col.semantic_comment ?? null,
        name: col.name ?? "",
        range: col.range ?? "",
        data_type: col.data_type ?? "",
      })),
    })),
  }));

  if (isError || isMetadataError) {
    return (
      <CustomContainer title={t("title")}>
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">{t("notFound")}</p>
        </div>
      </CustomContainer>
    );
  }

  if (!dataset || isMetadataLoading || localWorksheets.length === 0) {
    return (
      <CustomContainer title={t("title")}>
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">{t("loading")}</p>
        </div>
      </CustomContainer>
    );
  }

  // 构建容器面包屑导航
  const containerBreadcrumbs = [
    {
      title: t("backToDatasets"),
      href: "/datasets",
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: dataset.name || t("title"),
      href: `/datasets/file/${datasetId}`,
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: t("metadataConfig"),
      href: `/datasets/file/${datasetId}/${docId}`,
      isCurrent: true,
    },
  ];

  return (
    <CustomContainer
      title={`${t("metadataConfig")} (${hasChanges ? t("hasUnsavedChanges") : t("configNote")})`}
      breadcrumbs={containerBreadcrumbs}
      action={
        <Button
          size="sm"
          onClick={handleBuildDataset}
          disabled={saveAndParseMutation.isPending || !!dataset.building_version || !hasChanges}
        >
          {saveAndParseMutation.isPending ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {t("parsing")}
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              {t("saveAndParse")}
            </>
          )}
        </Button>
      }
    >
      {/* 左右布局 */}
      <div className="grid h-[calc(100vh-300px)] grid-cols-12 gap-6">
        {/* 左侧树形结构 */}
        <WorksheetList
          worksheets={worksheetDataList as any}
          navigationState={navigationState}
          onWorksheetSelect={handleWorksheetSelect}
          onSubtableSelect={handleSubtableSelect}
        />

        {/* 右侧内容区域 */}
        <div className="col-span-9 h-[calc(100vh-300px)] space-y-4">
          {navigationState.worksheetIndex !== undefined ? (
            <>
              {/* 面包屑导航 */}
              <Breadcrumb className="mb-4">
                <BreadcrumbList>
                  {breadcrumbs.map((item, index) => (
                    <div key={item.name} className="flex items-center">
                      {index > 0 && <BreadcrumbSeparator />}
                      <BreadcrumbItem>
                        <BreadcrumbLink
                          asChild
                          onClick={() => {
                            if (index === 0) {
                              setNavigationState({});
                              setBreadcrumbs([{ name: t("file"), type: "worksheets" }]);
                            } else if (index === 1) {
                              setNavigationState({
                                worksheetIndex: navigationState.worksheetIndex,
                              });
                              setBreadcrumbs([
                                { name: t("file"), type: "worksheets" },
                                {
                                  name: `${item.name}(${t("worksheet")})`,
                                  type: "worksheet",
                                },
                              ]);
                            }
                          }}
                        >
                          <div>{item.name}</div>
                        </BreadcrumbLink>
                      </BreadcrumbItem>
                    </div>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>

              {/* 内容标题 */}
              <div className="flex items-center gap-2">
                <Table className="text-primary h-4 w-4" />
                <h2 className="text-lg font-medium">{breadcrumbs[breadcrumbs.length - 1]?.name}</h2>
              </div>

              {/* 内容展示 */}
              <WorksheetContent
                worksheets={worksheetDataList as any}
                navigationState={navigationState}
                onWorksheetUpdate={handleWorksheetUpdate}
              />
            </>
          ) : (
            <div className="text-muted-foreground flex h-64 items-center justify-center">
              <div className="text-center">
                <FileText className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>{t("selectWorksheetPrompt")}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomContainer>
  );
}
